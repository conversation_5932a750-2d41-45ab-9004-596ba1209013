#!/usr/bin/env python3
"""
Simplified Keno Predictor - <PERSON>h giản chỉ tập trung vào test top hit
"""

import numpy as np
import mysql.connector
from datetime import datetime, timedelta
from variable_length_model import VariableLengthKenoModel, connect_db

class SimplifiedKenoPredictor:
    """Simplified Keno Predictor - Chỉ tập trung test top hit"""
    
    def __init__(self):
        # Load LSTM model
        try:
            self.lstm_model = VariableLen
            
            gthKenoModel()
            self.lstm_model.model = None  # Sẽ load khi cần
            print("✅ LSTM Model initialized")
        except:
            self.lstm_model = None
            print("⚠️ LSTM Model not available, using frequency fallback")
        
        # C<PERSON>u hình consecutive exclusion
        self.consecutive_window = 3  # Số kì liên tục để loại bỏ
        self.consecutive_threshold = 2  # Số lần xuất hiện để loại bỏ
        
        print("✅ Simplified Keno Predictor initialized")
    
    def get_day_draws(self, date):
        """L<PERSON>y tất cả kì của một ngày"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)
            
            query = """
                SELECT time, results, period
                FROM histories_keno 
                WHERE date = %s 
                ORDER BY time ASC
            """
            
            cursor.execute(query, (date,))
            rows = cursor.fetchall()
            
            cursor.close()
            conn.close()
            
            # Chuyển đổi results từ string thành list
            for row in rows:
                row['results'] = [int(n) for n in row['results'].split(',')]
            
            return rows
            
        except Exception as e:
            print(f"❌ Error loading day draws: {e}")
            return []
    
    def get_lstm_predictions(self, day_results, num_predictions=10):
        """Lấy số dự đoán từ LSTM model"""
        if not self.lstm_model:
            # Fallback: Dự đoán dựa trên frequency analysis
            return self._get_frequency_based_predictions(day_results, num_predictions)

        try:
            # Dự đoán missing numbers (số có khả năng trượt cao)
            predictions = self.lstm_model.predict_missing_numbers(day_results, num_miss=num_predictions)
            if predictions and len(predictions) >= num_predictions:
                return predictions[:num_predictions]
            else:
                return self._get_frequency_based_predictions(day_results, num_predictions)
        except Exception as e:
            return self._get_frequency_based_predictions(day_results, num_predictions)

    def get_lstm_6_predictions(self, day_results):
        """Lấy 6 số dự đoán chính từ LSTM model"""
        lstm_10 = self.get_lstm_predictions(day_results, 10)
        return lstm_10[:6]  # Lấy top 6 số từ top 10

    def _get_frequency_based_predictions(self, day_results, num_predictions=10):
        """Fallback: Dự đoán dựa trên frequency analysis"""
        if not day_results:
            import random
            return random.sample(range(1, 81), num_predictions)

        # Đếm frequency của từng số
        frequency = {}
        for draw in day_results:
            for num in draw:
                frequency[num] = frequency.get(num, 0) + 1

        # Lấy những số xuất hiện ít nhất (có khả năng trượt cao)
        all_numbers = list(range(1, 81))
        sorted_by_freq = sorted(all_numbers, key=lambda x: frequency.get(x, 0))

        return sorted_by_freq[:num_predictions]
    
    def get_excluded_numbers(self, day_results):
        """Lấy danh sách số bị loại bỏ (consecutive exclusion)"""
        if not day_results or len(day_results) < self.consecutive_window:
            return set()
        
        # Lấy consecutive_window kì gần nhất
        recent_draws = day_results[-self.consecutive_window:]
        excluded_numbers = set()
        
        # Đếm số lần xuất hiện của mỗi số
        for num in range(1, 81):
            appearance_count = 0
            for draw in recent_draws:
                if num in draw:
                    appearance_count += 1
            
            # Nếu xuất hiện >= threshold lần, loại bỏ
            if appearance_count >= self.consecutive_threshold:
                excluded_numbers.add(num)
        
        return excluded_numbers

    def get_least_frequent_numbers(self, input_draws, num_numbers=10):
        """Lấy 10 số ít xuất hiện nhất trong các kì của ngày"""
        # Đếm frequency của từng số
        frequency = {}
        for draw in input_draws:
            for num in draw:
                frequency[num] = frequency.get(num, 0) + 1

        # Sắp xếp theo frequency tăng dần (ít xuất hiện nhất trước)
        all_numbers = list(range(1, 81))
        sorted_by_freq = sorted(all_numbers, key=lambda x: frequency.get(x, 0))

        return sorted_by_freq[:num_numbers]

    def get_seasonal_pattern_top_10(self, input_draws, period_number):
        """Lấy top 10 số từ Seasonal Pattern Recognition"""
        try:
            # Phân tích theo khung giờ
            if period_number <= 60:
                # Sáng: Ưu tiên số lẻ
                candidates = [num for num in range(1, 81) if num % 2 == 1]
            elif period_number <= 90:
                # Chiều: Ưu tiên số trung bình (30-50)
                candidates = list(range(30, 51))
            else:
                # Tối: Ưu tiên số cao (60-80)
                candidates = list(range(60, 81))

            # Phân tích tần suất trượt của candidates trong dữ liệu gần đây
            recent_draws = input_draws[-50:] if len(input_draws) >= 50 else input_draws
            missing_freq = {}

            for num in candidates:
                missing_count = 0
                for draw in recent_draws:
                    if num not in draw:
                        missing_count += 1
                missing_freq[num] = missing_count / len(recent_draws) if recent_draws else 0

            # Sắp xếp và lấy top 10
            sorted_candidates = sorted(missing_freq.items(), key=lambda x: x[1], reverse=True)
            top_10 = [num for num, _ in sorted_candidates[:10]]

            # Đảm bảo có đủ 10 số
            if len(top_10) < 10:
                remaining = [num for num in range(1, 81) if num not in top_10]
                top_10.extend(remaining[:10-len(top_10)])

            return top_10[:10]
        except:
            return list(range(1, 11))  # Fallback

    def test_period(self, day_draws, period_index, min_periods=50):
        """Test dự đoán cho một kì cụ thể"""
        if period_index >= len(day_draws):
            return None

        # Lấy dữ liệu từ đầu ngày đến kì hiện tại - 1
        input_draws = [day_draws[i]['results'] for i in range(period_index)]

        if len(input_draws) < min_periods:  # Cần ít nhất min_periods kì để dự đoán
            return None
        
        # 1. Lấy 10 số LSTM dự đoán và 6 số chính
        lstm_10_predictions = self.get_lstm_predictions(input_draws, 10)
        lstm_6_predictions = lstm_10_predictions[:6]

        # 2. Các số bị loại khỏi danh sách dự đoán
        excluded_numbers = self.get_excluded_numbers(input_draws)

        # 2.1. LSTM Combined = LSTM - Excluded (loại bỏ excluded khỏi LSTM)
        lstm_combined = [num for num in lstm_10_predictions if num not in excluded_numbers]
        # Nếu sau khi loại bỏ không đủ 10 số, lấy thêm từ LSTM mở rộng
        if len(lstm_combined) < 10:
            # Lấy thêm LSTM predictions (mở rộng ra 20 số)
            extended_lstm = self.get_lstm_predictions(input_draws, 20)
            for num in extended_lstm:
                if num not in lstm_combined and num not in excluded_numbers:
                    lstm_combined.append(num)
                    if len(lstm_combined) >= 10:
                        break

        # 3. Lấy 10 số ít xuất hiện nhất trong ngày
        least_frequent_numbers = self.get_least_frequent_numbers(input_draws, 10)

        # 3.1. Lấy 10 số từ Seasonal Pattern Recognition
        period_number = period_index + 1
        seasonal_pattern_numbers = self.get_seasonal_pattern_top_10(input_draws, period_number)

        # 3.2. Seasonal Combined = Seasonal - Excluded
        seasonal_combined = [num for num in seasonal_pattern_numbers if num not in excluded_numbers]
        # Nếu sau khi loại bỏ không đủ 10 số, lấy thêm từ seasonal mở rộng
        if len(seasonal_combined) < 10:
            # Lấy thêm seasonal numbers (mở rộng logic)
            for num in range(1, 81):
                if num not in seasonal_combined and num not in excluded_numbers:
                    seasonal_combined.append(num)
                    if len(seasonal_combined) >= 10:
                        break

        # 4. Input mới = Least Frequent - Excluded (loại bỏ excluded khỏi least frequent)
        combined_numbers = [num for num in least_frequent_numbers if num not in excluded_numbers]
        # Nếu sau khi loại bỏ không đủ 10 số, lấy thêm từ least frequent mở rộng
        if len(combined_numbers) < 10:
            # Lấy thêm least frequent numbers (mở rộng ra 20 số)
            extended_least = self.get_least_frequent_numbers(input_draws, 20)
            for num in extended_least:
                if num not in combined_numbers and num not in excluded_numbers:
                    combined_numbers.append(num)
                    if len(combined_numbers) >= 10:
                        break

        # Lấy kết quả thực tế của kì này
        actual_results = day_draws[period_index]['results']
        actual_missing = set(range(1, 81)) - set(actual_results)

        # Kiểm tra hit của 6 số dự đoán chính
        lstm_6_hits = sum(1 for num in lstm_6_predictions if num in actual_missing)

        # Kiểm tra top hit của 10 số LSTM (theo vị trí)
        lstm_top_hits = []
        lstm_hit_details = []
        for i, num in enumerate(lstm_10_predictions):
            is_hit = num in actual_missing
            if is_hit:
                lstm_top_hits.append(i + 1)  # Vị trí top (1-10)
            lstm_hit_details.append({'position': i+1, 'number': num, 'hit': is_hit})

        # Kiểm tra hit của LSTM Combined (LSTM - Excluded)
        lstm_combined_hits = []
        lstm_combined_hit_details = []
        for i, num in enumerate(lstm_combined[:10]):
            is_hit = num in actual_missing
            if is_hit:
                lstm_combined_hits.append(i + 1)
            lstm_combined_hit_details.append({'number': num, 'hit': is_hit})

        # Kiểm tra hit của excluded numbers
        excluded_hits = []
        excluded_list = list(excluded_numbers)
        excluded_hit_details = []
        for i, num in enumerate(excluded_list):
            is_hit = num in actual_missing
            if is_hit:
                excluded_hits.append(i + 1)
            excluded_hit_details.append({'number': num, 'hit': is_hit})

        # Kiểm tra hit của least frequent numbers
        least_frequent_hits = []
        least_frequent_hit_details = []
        for i, num in enumerate(least_frequent_numbers):
            is_hit = num in actual_missing
            if is_hit:
                least_frequent_hits.append(i + 1)
            least_frequent_hit_details.append({'number': num, 'hit': is_hit})

        # Kiểm tra hit của seasonal pattern numbers
        seasonal_hits = []
        seasonal_hit_details = []
        for i, num in enumerate(seasonal_pattern_numbers):
            is_hit = num in actual_missing
            if is_hit:
                seasonal_hits.append(i + 1)
            seasonal_hit_details.append({'number': num, 'hit': is_hit})

        # Kiểm tra hit của seasonal combined numbers
        seasonal_combined_hits = []
        seasonal_combined_hit_details = []
        for i, num in enumerate(seasonal_combined[:10]):
            is_hit = num in actual_missing
            if is_hit:
                seasonal_combined_hits.append(i + 1)
            seasonal_combined_hit_details.append({'number': num, 'hit': is_hit})

        # Kiểm tra hit của combined numbers (top 10)
        combined_hits = []
        combined_hit_details = []
        for i, num in enumerate(combined_numbers[:10]):
            is_hit = num in actual_missing
            if is_hit:
                combined_hits.append(i + 1)
            combined_hit_details.append({'number': num, 'hit': is_hit})

        # Tạo Top 9 Strategy: Sắp xếp theo thứ tự Top1, Top2, Top3 từ mỗi strategy
        # Xử lý trùng lặp bằng cách thay thế số trùng bằng số tiếp theo
        top9_strategy = []
        excluded_list_for_top9 = list(excluded_numbers)
        used_numbers = set()

        def get_next_available(strategy_list, start_index, used_set):
            """Lấy số tiếp theo chưa được sử dụng từ strategy"""
            for i in range(start_index, len(strategy_list)):
                if strategy_list[i] not in used_set:
                    return strategy_list[i]
            return None

        # Bước 1: Lấy Top 1 từ mỗi strategy (vị trí 1-3)
        # Vị trí 1: Top 1 từ LSTM Original
        if len(lstm_10_predictions) >= 1:
            num = get_next_available(lstm_10_predictions, 0, used_numbers)
            if num is not None:
                top9_strategy.append(num)
                used_numbers.add(num)

        # Vị trí 2: Top 1 từ Least Frequent Original
        if len(least_frequent_numbers) >= 1:
            num = get_next_available(least_frequent_numbers, 0, used_numbers)
            if num is not None:
                top9_strategy.append(num)
                used_numbers.add(num)

        # Vị trí 3: Top 1 từ Excluded
        if len(excluded_list_for_top9) >= 1:
            num = get_next_available(excluded_list_for_top9, 0, used_numbers)
            if num is not None:
                top9_strategy.append(num)
                used_numbers.add(num)

        # Bước 2: Lấy Top 2 từ mỗi strategy (vị trí 4-6)
        # Vị trí 4: Top 2 từ LSTM Original
        if len(lstm_10_predictions) >= 2:
            num = get_next_available(lstm_10_predictions, 1, used_numbers)
            if num is not None:
                top9_strategy.append(num)
                used_numbers.add(num)

        # Vị trí 5: Top 2 từ Least Frequent Original
        if len(least_frequent_numbers) >= 2:
            num = get_next_available(least_frequent_numbers, 1, used_numbers)
            if num is not None:
                top9_strategy.append(num)
                used_numbers.add(num)

        # Vị trí 6: Top 2 từ Excluded
        if len(excluded_list_for_top9) >= 2:
            num = get_next_available(excluded_list_for_top9, 1, used_numbers)
            if num is not None:
                top9_strategy.append(num)
                used_numbers.add(num)

        # Bước 3: Lấy Top 3 từ mỗi strategy (vị trí 7-9)
        # Vị trí 7: Top 3 từ LSTM Original
        if len(lstm_10_predictions) >= 3:
            num = get_next_available(lstm_10_predictions, 2, used_numbers)
            if num is not None:
                top9_strategy.append(num)
                used_numbers.add(num)

        # Vị trí 8: Top 3 từ Least Frequent Original
        if len(least_frequent_numbers) >= 3:
            num = get_next_available(least_frequent_numbers, 2, used_numbers)
            if num is not None:
                top9_strategy.append(num)
                used_numbers.add(num)

        # Vị trí 9: Top 3 từ Excluded
        if len(excluded_list_for_top9) >= 3:
            num = get_next_available(excluded_list_for_top9, 2, used_numbers)
            if num is not None:
                top9_strategy.append(num)
                used_numbers.add(num)

        # Đảm bảo có đúng 9 số (nếu thiếu)
        if len(top9_strategy) < 9:
            all_candidates = lstm_10_predictions + least_frequent_numbers + excluded_list_for_top9
            for num in all_candidates:
                if num not in used_numbers:
                    top9_strategy.append(num)
                    used_numbers.add(num)
                    if len(top9_strategy) >= 9:
                        break

        top9_strategy = top9_strategy[:9]  # Chỉ lấy 9 số đầu

        # Kiểm tra hit của Top 9 Strategy
        top9_hits = []
        top9_hit_details = []
        for i, num in enumerate(top9_strategy):
            is_hit = num in actual_missing
            if is_hit:
                top9_hits.append(i + 1)
            top9_hit_details.append({'number': num, 'hit': is_hit})
        
        return {
            'period': period_index + 1,
            'time': day_draws[period_index]['time'],
            'actual_results': actual_results,
            'actual_missing': sorted(list(actual_missing)),
            'lstm_10_predictions': lstm_10_predictions,
            'lstm_6_predictions': lstm_6_predictions,
            'lstm_6_hits': lstm_6_hits,
            'lstm_hit_details': lstm_hit_details,
            'lstm_top_hits': lstm_top_hits,
            'lstm_combined': lstm_combined[:10],
            'lstm_combined_hit_details': lstm_combined_hit_details,
            'lstm_combined_hits': lstm_combined_hits,
            'excluded_numbers': excluded_list,
            'excluded_hit_details': excluded_hit_details,
            'excluded_hits': excluded_hits,
            'least_frequent_numbers': least_frequent_numbers,
            'least_frequent_hit_details': least_frequent_hit_details,
            'least_frequent_hits': least_frequent_hits,
            'seasonal_pattern_numbers': seasonal_pattern_numbers,
            'seasonal_hit_details': seasonal_hit_details,
            'seasonal_hits': seasonal_hits,
            'seasonal_combined': seasonal_combined[:10],
            'seasonal_combined_hit_details': seasonal_combined_hit_details,
            'seasonal_combined_hits': seasonal_combined_hits,
            'combined_numbers': combined_numbers[:10],
            'combined_hit_details': combined_hit_details,
            'combined_hits': combined_hits,
            'top9_strategy': top9_strategy,
            'top9_hit_details': top9_hit_details,
            'top9_hits': top9_hits,
            'lstm_hit_count': len(lstm_top_hits),
            'lstm_combined_hit_count': len(lstm_combined_hits),
            'excluded_hit_count': len(excluded_hits),
            'least_frequent_hit_count': len(least_frequent_hits),
            'seasonal_hit_count': len(seasonal_hits),
            'seasonal_combined_hit_count': len(seasonal_combined_hits),
            'combined_hit_count': len(combined_hits),
            'top9_hit_count': len(top9_hits)
        }
    
    def test_date_range(self, start_date, end_date, min_periods=50):
        """Test từ ngày start_date đến end_date"""
        print(f"\n🎯 TESTING DATE RANGE: {start_date} → {end_date}")
        print(f"⚙️ Config: Minimum periods = {min_periods}")
        print("="*60)
        
        # Generate list of dates
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        
        test_dates = []
        current_date = start_dt
        while current_date <= end_dt:
            test_dates.append(current_date.strftime("%Y-%m-%d"))
            current_date += timedelta(days=1)
        
        # Thống kê tổng hợp
        total_stats = {
            'lstm_top_hits': [0] * 10,  # [top1_hits, top2_hits, ..., top10_hits]
            'lstm_combined_top_hits': [0] * 10,
            'excluded_top_hits': [0] * 10,
            'seasonal_top_hits': [0] * 10,
            'seasonal_combined_top_hits': [0] * 10,
            'least_frequent_top_hits': [0] * 10,
            'least_combined_top_hits': [0] * 10,
            'top9_strategy_hits': [0] * 9,
            'total_predictions': 0,
            'total_days': 0
        }
        
        # Thống kê theo ngày
        daily_stats = {}
        
        for test_date in test_dates:
            print(f"\n📅 Testing {test_date}...")
            
            day_draws = self.get_day_draws(test_date)
            if len(day_draws) < min_periods + 1:  # Cần ít nhất min_periods + 1 kì
                print(f"   ⚠️ Chỉ có {len(day_draws)} kì, cần ít nhất {min_periods + 1}, bỏ qua")
                continue

            # Thống kê ngày này
            day_stats = {
                'lstm_top_hits': [0] * 10,
                'lstm_combined_top_hits': [0] * 10,
                'excluded_top_hits': [0] * 10,
                'seasonal_top_hits': [0] * 10,
                'seasonal_combined_top_hits': [0] * 10,
                'least_frequent_top_hits': [0] * 10,
                'least_combined_top_hits': [0] * 10,
                'top9_strategy_hits': [0] * 9,
                'predictions_count': 0,
                'sample_lstm': None,
                'sample_excluded': None,
                'sample_seasonal': None,
                'sample_least_frequent': None,
                'sample_top9': None
            }

            # Test từ kì min_periods+1 đến cuối ngày (tối đa kì 119)
            max_period = min(len(day_draws), 119)
            print(f"   Testing periods {min_periods+1}-{max_period} ({max_period - min_periods} periods)")

            for period_index in range(min_periods, max_period):
                result = self.test_period(day_draws, period_index, min_periods)
                
                if result:
                    day_stats['predictions_count'] += 1
                    total_stats['total_predictions'] += 1

                    # Lưu sample predictions cho debug (lần đầu tiên)
                    if day_stats['sample_lstm'] is None:
                        day_stats['sample_lstm'] = result['lstm_10_predictions']
                        day_stats['sample_excluded'] = result['excluded_numbers']
                        day_stats['sample_seasonal'] = result['seasonal_pattern_numbers']
                        day_stats['sample_least_frequent'] = result['least_frequent_numbers']
                        day_stats['sample_top9'] = result['top9_strategy']

                    # Cập nhật thống kê LSTM hits
                    for hit_pos in result['lstm_top_hits']:
                        if 1 <= hit_pos <= 10:
                            day_stats['lstm_top_hits'][hit_pos - 1] += 1
                            total_stats['lstm_top_hits'][hit_pos - 1] += 1

                    # Cập nhật thống kê LSTM Combined hits
                    for hit_pos in result['lstm_combined_hits']:
                        if 1 <= hit_pos <= 10:
                            day_stats['lstm_combined_top_hits'][hit_pos - 1] += 1
                            total_stats['lstm_combined_top_hits'][hit_pos - 1] += 1

                    # Cập nhật thống kê excluded hits
                    for hit_pos in result['excluded_hits']:
                        if 1 <= hit_pos <= 10:
                            day_stats['excluded_top_hits'][hit_pos - 1] += 1
                            total_stats['excluded_top_hits'][hit_pos - 1] += 1

                    # Cập nhật thống kê Seasonal hits
                    for hit_pos in result['seasonal_hits']:
                        if 1 <= hit_pos <= 10:
                            day_stats['seasonal_top_hits'][hit_pos - 1] += 1
                            total_stats['seasonal_top_hits'][hit_pos - 1] += 1

                    # Cập nhật thống kê Seasonal Combined hits
                    for hit_pos in result['seasonal_combined_hits']:
                        if 1 <= hit_pos <= 10:
                            day_stats['seasonal_combined_top_hits'][hit_pos - 1] += 1
                            total_stats['seasonal_combined_top_hits'][hit_pos - 1] += 1

                    # Cập nhật thống kê Least Frequent hits
                    for hit_pos in result['least_frequent_hits']:
                        if 1 <= hit_pos <= 10:
                            day_stats['least_frequent_top_hits'][hit_pos - 1] += 1
                            total_stats['least_frequent_top_hits'][hit_pos - 1] += 1

                    # Cập nhật thống kê Least Combined hits
                    for hit_pos in result['combined_hits']:
                        if 1 <= hit_pos <= 10:
                            day_stats['least_combined_top_hits'][hit_pos - 1] += 1
                            total_stats['least_combined_top_hits'][hit_pos - 1] += 1

                    # Cập nhật thống kê Top 9 Strategy hits
                    for hit_pos in result['top9_hits']:
                        if 1 <= hit_pos <= 9:
                            day_stats['top9_strategy_hits'][hit_pos - 1] += 1
                            total_stats['top9_strategy_hits'][hit_pos - 1] += 1

                    # Log chi tiết cho tất cả các kì
                    self._print_period_details(result)
            
            if day_stats['predictions_count'] > 0:
                daily_stats[test_date] = day_stats
                total_stats['total_days'] += 1
                print(f"   📊 Tổng: {day_stats['predictions_count']} predictions")
        
        # Hiển thị thống kê
        self._display_results(total_stats, daily_stats, start_date, end_date)
        
        return {
            'total_stats': total_stats,
            'daily_stats': daily_stats
        }

    def _print_period_details(self, result):
        """In chi tiết kết quả của một kì"""
        print(f"\n   🎯 KÌ {result['period']:3d} - {result['time']}")
        print(f"      📊 Kết quả thực tế: {result['actual_results']}")

        # 1. In 6 số dự đoán và kết quả
        print(f"      🤖 6 số dự đoán LSTM: {result['lstm_6_predictions']} → {result['lstm_6_hits']}/6 hits")

        # 2. In top1-10 LSTM theo hàng ngang
        print(f"      📈 LSTM Original:")
        numbers_line = " ".join([f"{detail['number']:2d}" for detail in result['lstm_hit_details']])
        hits_line = " ".join([f"{'✅' if detail['hit'] else '❌'}" for detail in result['lstm_hit_details']])
        print(f"         Numbers: {numbers_line}")
        print(f"         Hits:    {hits_line}")

        # 2.1. In LSTM Combined theo hàng ngang
        print(f"      🔗 LSTM Combined:")
        numbers_line = " ".join([f"{detail['number']:2d}" for detail in result['lstm_combined_hit_details']])
        hits_line = " ".join([f"{'✅' if detail['hit'] else '❌'}" for detail in result['lstm_combined_hit_details']])
        print(f"         Numbers: {numbers_line}")
        print(f"         Hits:    {hits_line}")

        # 3. In excluded numbers theo hàng ngang
        if result['excluded_hit_details']:
            print(f"      🚫 Excluded:")
            excluded_display = result['excluded_hit_details'][:10]  # Chỉ hiển thị 10 số đầu
            numbers_line = " ".join([f"{detail['number']:2d}" for detail in excluded_display])
            hits_line = " ".join([f"{'✅' if detail['hit'] else '❌'}" for detail in excluded_display])
            print(f"         Numbers: {numbers_line}")
            print(f"         Hits:    {hits_line}")
        else:
            print(f"      🚫 Excluded: Không có")

        # 4. In seasonal pattern theo hàng ngang
        print(f"      🌟 Seasonal Original:")
        numbers_line = " ".join([f"{detail['number']:2d}" for detail in result['seasonal_hit_details']])
        hits_line = " ".join([f"{'✅' if detail['hit'] else '❌'}" for detail in result['seasonal_hit_details']])
        print(f"         Numbers: {numbers_line}")
        print(f"         Hits:    {hits_line}")

        # 4.1. In seasonal combined theo hàng ngang
        print(f"      🔗 Seasonal Combined:")
        numbers_line = " ".join([f"{detail['number']:2d}" for detail in result['seasonal_combined_hit_details']])
        hits_line = " ".join([f"{'✅' if detail['hit'] else '❌'}" for detail in result['seasonal_combined_hit_details']])
        print(f"         Numbers: {numbers_line}")
        print(f"         Hits:    {hits_line}")

        # 5. In least frequent theo hàng ngang
        print(f"      📉 Least Frequent Original:")
        numbers_line = " ".join([f"{detail['number']:2d}" for detail in result['least_frequent_hit_details']])
        hits_line = " ".join([f"{'✅' if detail['hit'] else '❌'}" for detail in result['least_frequent_hit_details']])
        print(f"         Numbers: {numbers_line}")
        print(f"         Hits:    {hits_line}")

        # 6. In combined (least - excluded) theo hàng ngang
        print(f"      🔗 Least Combined:")
        numbers_line = " ".join([f"{detail['number']:2d}" for detail in result['combined_hit_details']])
        hits_line = " ".join([f"{'✅' if detail['hit'] else '❌'}" for detail in result['combined_hit_details']])
        print(f"         Numbers: {numbers_line}")
        print(f"         Hits:    {hits_line}")

        # 7. In Top 9 Strategy theo hàng ngang
        print(f"      🎯 Top 9 Strategy (Top1,2,3 từ LSTM+Least+Excluded):")
        numbers_line = " ".join([f"{detail['number']:2d}" for detail in result['top9_hit_details']])
        hits_line = " ".join([f"{'✅' if detail['hit'] else '❌'}" for detail in result['top9_hit_details']])
        print(f"         Numbers: {numbers_line}")
        print(f"         Hits:    {hits_line}")
        print(f"         Order:   T1 T1 T1 T2 T2 T2 T3 T3 T3")
        print(f"         Source:  LM LF EX LM LF EX LM LF EX")

        print(f"      📊 Tổng kết:")
        print(f"         LSTM: {result['lstm_hit_count']}/10, LSTM Combined: {result['lstm_combined_hit_count']}/10")
        print(f"         Seasonal: {result['seasonal_hit_count']}/10, Seasonal Combined: {result['seasonal_combined_hit_count']}/10")
        print(f"         Least: {result['least_frequent_hit_count']}/10, Least Combined: {result['combined_hit_count']}/10")
        print(f"         Excluded: {result['excluded_hit_count']}/{len(result['excluded_numbers'])}")
        print(f"         🎯 Top 9 Strategy: {result['top9_hit_count']}/9")

    def _display_results(self, total_stats, daily_stats, start_date, end_date):
        """Hiển thị kết quả thống kê"""
        print(f"\n📊 THỐNG KÊ TOP HIT THEO NGÀY")
        print("="*100)

        for test_date, day_stats in daily_stats.items():
            print(f"\n📅 {test_date} ({day_stats['predictions_count']} predictions):")
            print("-"*100)

            # LSTM Original top hits
            print("🤖 LSTM Original Top Hits:")
            lstm_rates = []
            for i in range(10):
                hit_count = day_stats['lstm_top_hits'][i]
                hit_rate = (hit_count / day_stats['predictions_count']) * 100 if day_stats['predictions_count'] > 0 else 0
                lstm_rates.append(f"Top{i+1}: {hit_rate:.1f}%")
            print("   " + " | ".join(lstm_rates))

            # LSTM Combined top hits
            print("🔗 LSTM Combined Top Hits:")
            lstm_combined_rates = []
            for i in range(10):
                hit_count = day_stats['lstm_combined_top_hits'][i]
                hit_rate = (hit_count / day_stats['predictions_count']) * 100 if day_stats['predictions_count'] > 0 else 0
                lstm_combined_rates.append(f"Top{i+1}: {hit_rate:.1f}%")
            print("   " + " | ".join(lstm_combined_rates))

            # Seasonal Original top hits
            print("🌟 Seasonal Original Top Hits:")
            seasonal_rates = []
            for i in range(10):
                hit_count = day_stats['seasonal_top_hits'][i]
                hit_rate = (hit_count / day_stats['predictions_count']) * 100 if day_stats['predictions_count'] > 0 else 0
                seasonal_rates.append(f"Top{i+1}: {hit_rate:.1f}%")
            print("   " + " | ".join(seasonal_rates))

            # Seasonal Combined top hits
            print("🔗 Seasonal Combined Top Hits:")
            seasonal_combined_rates = []
            for i in range(10):
                hit_count = day_stats['seasonal_combined_top_hits'][i]
                hit_rate = (hit_count / day_stats['predictions_count']) * 100 if day_stats['predictions_count'] > 0 else 0
                seasonal_combined_rates.append(f"Top{i+1}: {hit_rate:.1f}%")
            print("   " + " | ".join(seasonal_combined_rates))

            # Least Frequent Original top hits
            print("📉 Least Frequent Original Top Hits:")
            least_rates = []
            for i in range(10):
                hit_count = day_stats['least_frequent_top_hits'][i]
                hit_rate = (hit_count / day_stats['predictions_count']) * 100 if day_stats['predictions_count'] > 0 else 0
                least_rates.append(f"Top{i+1}: {hit_rate:.1f}%")
            print("   " + " | ".join(least_rates))

            # Least Combined top hits
            print("🔗 Least Combined Top Hits:")
            least_combined_rates = []
            for i in range(10):
                hit_count = day_stats['least_combined_top_hits'][i]
                hit_rate = (hit_count / day_stats['predictions_count']) * 100 if day_stats['predictions_count'] > 0 else 0
                least_combined_rates.append(f"Top{i+1}: {hit_rate:.1f}%")
            print("   " + " | ".join(least_combined_rates))

            # Excluded top hits
            print("🚫 Excluded Top Hits:")
            excluded_rates = []
            for i in range(10):
                hit_count = day_stats['excluded_top_hits'][i]
                hit_rate = (hit_count / day_stats['predictions_count']) * 100 if day_stats['predictions_count'] > 0 else 0
                excluded_rates.append(f"Top{i+1}: {hit_rate:.1f}%")
            print("   " + " | ".join(excluded_rates))

            # Top 9 Strategy top hits
            print("🎯 Top 9 Strategy Top Hits:")
            top9_rates = []
            for i in range(9):
                hit_count = day_stats['top9_strategy_hits'][i]
                hit_rate = (hit_count / day_stats['predictions_count']) * 100 if day_stats['predictions_count'] > 0 else 0
                top9_rates.append(f"Top{i+1}: {hit_rate:.1f}%")
            print("   " + " | ".join(top9_rates))

            # Hiển thị sample predictions để debug
            print(f"   📝 Sample LSTM: {day_stats.get('sample_lstm', [])}")
            print(f"   📝 Sample Seasonal: {day_stats.get('sample_seasonal', [])}")
            print(f"   📝 Sample Least Frequent: {day_stats.get('sample_least_frequent', [])}")
            print(f"   📝 Sample Excluded: {day_stats.get('sample_excluded', [])}")
            print(f"   📝 Sample Top 9 Strategy: {day_stats.get('sample_top9', [])}")
        
        # Thống kê tổng hợp
        print(f"\n🎯 THỐNG KÊ TỔNG HỢP ({start_date} → {end_date})")
        print("="*100)
        print(f"Tổng: {total_stats['total_days']} ngày, {total_stats['total_predictions']} predictions")
        
        print("\n🤖 LSTM Original Average Top Hit Rates:")
        lstm_avg_rates = []
        for i in range(10):
            hit_count = total_stats['lstm_top_hits'][i]
            hit_rate = (hit_count / total_stats['total_predictions']) * 100 if total_stats['total_predictions'] > 0 else 0
            lstm_avg_rates.append(f"Top{i+1}: {hit_rate:.1f}%")
        print("   " + " | ".join(lstm_avg_rates))

        print("\n🔗 LSTM Combined Average Top Hit Rates:")
        lstm_combined_avg_rates = []
        for i in range(10):
            hit_count = total_stats['lstm_combined_top_hits'][i]
            hit_rate = (hit_count / total_stats['total_predictions']) * 100 if total_stats['total_predictions'] > 0 else 0
            lstm_combined_avg_rates.append(f"Top{i+1}: {hit_rate:.1f}%")
        print("   " + " | ".join(lstm_combined_avg_rates))

        print("\n🌟 Seasonal Original Average Top Hit Rates:")
        seasonal_avg_rates = []
        for i in range(10):
            hit_count = total_stats['seasonal_top_hits'][i]
            hit_rate = (hit_count / total_stats['total_predictions']) * 100 if total_stats['total_predictions'] > 0 else 0
            seasonal_avg_rates.append(f"Top{i+1}: {hit_rate:.1f}%")
        print("   " + " | ".join(seasonal_avg_rates))

        print("\n🔗 Seasonal Combined Average Top Hit Rates:")
        seasonal_combined_avg_rates = []
        for i in range(10):
            hit_count = total_stats['seasonal_combined_top_hits'][i]
            hit_rate = (hit_count / total_stats['total_predictions']) * 100 if total_stats['total_predictions'] > 0 else 0
            seasonal_combined_avg_rates.append(f"Top{i+1}: {hit_rate:.1f}%")
        print("   " + " | ".join(seasonal_combined_avg_rates))

        print("\n📉 Least Frequent Original Average Top Hit Rates:")
        least_avg_rates = []
        for i in range(10):
            hit_count = total_stats['least_frequent_top_hits'][i]
            hit_rate = (hit_count / total_stats['total_predictions']) * 100 if total_stats['total_predictions'] > 0 else 0
            least_avg_rates.append(f"Top{i+1}: {hit_rate:.1f}%")
        print("   " + " | ".join(least_avg_rates))

        print("\n🔗 Least Combined Average Top Hit Rates:")
        least_combined_avg_rates = []
        for i in range(10):
            hit_count = total_stats['least_combined_top_hits'][i]
            hit_rate = (hit_count / total_stats['total_predictions']) * 100 if total_stats['total_predictions'] > 0 else 0
            least_combined_avg_rates.append(f"Top{i+1}: {hit_rate:.1f}%")
        print("   " + " | ".join(least_combined_avg_rates))

        print("\n🚫 Excluded Average Top Hit Rates:")
        excluded_avg_rates = []
        for i in range(10):
            hit_count = total_stats['excluded_top_hits'][i]
            hit_rate = (hit_count / total_stats['total_predictions']) * 100 if total_stats['total_predictions'] > 0 else 0
            excluded_avg_rates.append(f"Top{i+1}: {hit_rate:.1f}%")
        print("   " + " | ".join(excluded_avg_rates))

        print("\n🎯 Top 9 Strategy Average Top Hit Rates:")
        top9_avg_rates = []
        for i in range(9):
            hit_count = total_stats['top9_strategy_hits'][i]
            hit_rate = (hit_count / total_stats['total_predictions']) * 100 if total_stats['total_predictions'] > 0 else 0
            top9_avg_rates.append(f"Top{i+1}: {hit_rate:.1f}%")
        print("   " + " | ".join(top9_avg_rates))

        print("="*100)

def test_range(start_date, end_date, min_periods=50):
    """Function để test từ command line"""
    predictor = SimplifiedKenoPredictor()
    return predictor.test_date_range(start_date, end_date, min_periods)

if __name__ == "__main__":
    import sys

    if len(sys.argv) < 3 or len(sys.argv) > 4:
        print("Usage: python simplified_predictor.py START_DATE END_DATE [MIN_PERIODS]")
        print("Example: python simplified_keno_predictor.py 2025-04-01 2025-04-30")
        print("Example: python simplified_predictor.py 2025-04-01 2025-04-30 30")
        sys.exit(1)

    start_date = sys.argv[1]
    end_date = sys.argv[2]
    min_periods = int(sys.argv[3]) if len(sys.argv) == 4 else 50

    test_range(start_date, end_date, min_periods)
