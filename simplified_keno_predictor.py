#!/usr/bin/env python3
"""
Simplified Keno Predictor - <PERSON>h giản chỉ tập trung vào test top hit
"""

import numpy as np
import mysql.connector
from datetime import datetime, timedelta
from variable_length_model import VariableLengthKenoModel, connect_db

class SimplifiedKenoPredictor:
    """Simplified Keno Predictor - Chỉ tập trung test top hit"""
    
    def __init__(self):
        # Load LSTM model
        try:
            self.lstm_model = VariableLen
            
            gthKenoModel()
            self.lstm_model.model = None  # Sẽ load khi cần
            print("✅ LSTM Model initialized")
        except:
            self.lstm_model = None
            print("⚠️ LSTM Model not available, using frequency fallback")
        
        # C<PERSON>u hình consecutive exclusion
        self.consecutive_window = 3  # Số kì liên tục để loại bỏ
        self.consecutive_threshold = 2  # Số lần xuất hiện để loại bỏ
        
        print("✅ Simplified Keno Predictor initialized")
    
    def get_day_draws(self, date):
        """L<PERSON>y tất cả kì của một ngày"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)
            
            query = """
                SELECT time, results, period
                FROM histories_keno 
                WHERE date = %s 
                ORDER BY time ASC
            """
            
            cursor.execute(query, (date,))
            rows = cursor.fetchall()
            
            cursor.close()
            conn.close()
            
            # Chuyển đổi results từ string thành list
            for row in rows:
                row['results'] = [int(n) for n in row['results'].split(',')]
            
            return rows
            
        except Exception as e:
            print(f"❌ Error loading day draws: {e}")
            return []
    
    def get_lstm_predictions(self, day_results, num_predictions=10):
        """Lấy số dự đoán từ LSTM model"""
        if not self.lstm_model:
            # Fallback: Dự đoán dựa trên frequency analysis
            return self._get_frequency_based_predictions(day_results, num_predictions)

        try:
            # Dự đoán missing numbers (số có khả năng trượt cao)
            predictions = self.lstm_model.predict_missing_numbers(day_results, num_miss=num_predictions)
            if predictions and len(predictions) >= num_predictions:
                return predictions[:num_predictions]
            else:
                return self._get_frequency_based_predictions(day_results, num_predictions)
        except Exception as e:
            return self._get_frequency_based_predictions(day_results, num_predictions)

    def get_lstm_6_predictions(self, day_results):
        """Lấy 6 số dự đoán chính từ LSTM model"""
        lstm_10 = self.get_lstm_predictions(day_results, 10)
        return lstm_10[:6]  # Lấy top 6 số từ top 10

    def _get_frequency_based_predictions(self, day_results, num_predictions=10):
        """Fallback: Dự đoán dựa trên frequency analysis"""
        if not day_results:
            import random
            return random.sample(range(1, 81), num_predictions)

        # Đếm frequency của từng số
        frequency = {}
        for draw in day_results:
            for num in draw:
                frequency[num] = frequency.get(num, 0) + 1

        # Lấy những số xuất hiện ít nhất (có khả năng trượt cao)
        all_numbers = list(range(1, 81))
        sorted_by_freq = sorted(all_numbers, key=lambda x: frequency.get(x, 0))

        return sorted_by_freq[:num_predictions]
    
    def get_excluded_numbers(self, day_results):
        """Lấy danh sách số bị loại bỏ (consecutive exclusion)"""
        if not day_results or len(day_results) < self.consecutive_window:
            return set()
        
        # Lấy consecutive_window kì gần nhất
        recent_draws = day_results[-self.consecutive_window:]
        excluded_numbers = set()
        
        # Đếm số lần xuất hiện của mỗi số
        for num in range(1, 81):
            appearance_count = 0
            for draw in recent_draws:
                if num in draw:
                    appearance_count += 1
            
            # Nếu xuất hiện >= threshold lần, loại bỏ
            if appearance_count >= self.consecutive_threshold:
                excluded_numbers.add(num)
        
        return excluded_numbers

    def get_least_frequent_numbers(self, input_draws, num_numbers=10):
        """Lấy 10 số ít xuất hiện nhất trong các kì của ngày"""
        # Đếm frequency của từng số
        frequency = {}
        for draw in input_draws:
            for num in draw:
                frequency[num] = frequency.get(num, 0) + 1

        # Sắp xếp theo frequency tăng dần (ít xuất hiện nhất trước)
        all_numbers = list(range(1, 81))
        sorted_by_freq = sorted(all_numbers, key=lambda x: frequency.get(x, 0))

        return sorted_by_freq[:num_numbers]

    def test_period(self, day_draws, period_index, min_periods=50):
        """Test dự đoán cho một kì cụ thể"""
        if period_index >= len(day_draws):
            return None

        # Lấy dữ liệu từ đầu ngày đến kì hiện tại - 1
        input_draws = [day_draws[i]['results'] for i in range(period_index)]

        if len(input_draws) < min_periods:  # Cần ít nhất min_periods kì để dự đoán
            return None
        
        # 1. Lấy 10 số LSTM dự đoán và 6 số chính
        lstm_10_predictions = self.get_lstm_predictions(input_draws, 10)
        lstm_6_predictions = lstm_10_predictions[:6]

        # 2. Các số bị loại khỏi danh sách dự đoán
        excluded_numbers = self.get_excluded_numbers(input_draws)

        # 2.1. LSTM Combined = LSTM - Excluded (loại bỏ excluded khỏi LSTM)
        lstm_combined = [num for num in lstm_10_predictions if num not in excluded_numbers]
        # Nếu sau khi loại bỏ không đủ 10 số, lấy thêm từ LSTM mở rộng
        if len(lstm_combined) < 10:
            # Lấy thêm LSTM predictions (mở rộng ra 20 số)
            extended_lstm = self.get_lstm_predictions(input_draws, 20)
            for num in extended_lstm:
                if num not in lstm_combined and num not in excluded_numbers:
                    lstm_combined.append(num)
                    if len(lstm_combined) >= 10:
                        break

        # 3. Lấy 10 số ít xuất hiện nhất trong ngày
        least_frequent_numbers = self.get_least_frequent_numbers(input_draws, 10)

        # 4. Input mới = Least Frequent - Excluded (loại bỏ excluded khỏi least frequent)
        combined_numbers = [num for num in least_frequent_numbers if num not in excluded_numbers]
        # Nếu sau khi loại bỏ không đủ 10 số, lấy thêm từ least frequent mở rộng
        if len(combined_numbers) < 10:
            # Lấy thêm least frequent numbers (mở rộng ra 20 số)
            extended_least = self.get_least_frequent_numbers(input_draws, 20)
            for num in extended_least:
                if num not in combined_numbers and num not in excluded_numbers:
                    combined_numbers.append(num)
                    if len(combined_numbers) >= 10:
                        break

        # Lấy kết quả thực tế của kì này
        actual_results = day_draws[period_index]['results']
        actual_missing = set(range(1, 81)) - set(actual_results)

        # Kiểm tra hit của 6 số dự đoán chính
        lstm_6_hits = sum(1 for num in lstm_6_predictions if num in actual_missing)

        # Kiểm tra top hit của 10 số LSTM (theo vị trí)
        lstm_top_hits = []
        lstm_hit_details = []
        for i, num in enumerate(lstm_10_predictions):
            is_hit = num in actual_missing
            if is_hit:
                lstm_top_hits.append(i + 1)  # Vị trí top (1-10)
            lstm_hit_details.append({'position': i+1, 'number': num, 'hit': is_hit})

        # Kiểm tra hit của LSTM Combined (LSTM - Excluded)
        lstm_combined_hits = []
        lstm_combined_hit_details = []
        for i, num in enumerate(lstm_combined[:10]):
            is_hit = num in actual_missing
            if is_hit:
                lstm_combined_hits.append(i + 1)
            lstm_combined_hit_details.append({'number': num, 'hit': is_hit})

        # Kiểm tra hit của excluded numbers
        excluded_hits = []
        excluded_list = list(excluded_numbers)
        excluded_hit_details = []
        for i, num in enumerate(excluded_list):
            is_hit = num in actual_missing
            if is_hit:
                excluded_hits.append(i + 1)
            excluded_hit_details.append({'number': num, 'hit': is_hit})

        # Kiểm tra hit của least frequent numbers
        least_frequent_hits = []
        least_frequent_hit_details = []
        for i, num in enumerate(least_frequent_numbers):
            is_hit = num in actual_missing
            if is_hit:
                least_frequent_hits.append(i + 1)
            least_frequent_hit_details.append({'number': num, 'hit': is_hit})

        # Kiểm tra hit của combined numbers (top 10)
        combined_hits = []
        combined_hit_details = []
        for i, num in enumerate(combined_numbers[:10]):
            is_hit = num in actual_missing
            if is_hit:
                combined_hits.append(i + 1)
            combined_hit_details.append({'number': num, 'hit': is_hit})
        
        return {
            'period': period_index + 1,
            'time': day_draws[period_index]['time'],
            'actual_results': actual_results,
            'actual_missing': sorted(list(actual_missing)),
            'lstm_10_predictions': lstm_10_predictions,
            'lstm_6_predictions': lstm_6_predictions,
            'lstm_6_hits': lstm_6_hits,
            'lstm_hit_details': lstm_hit_details,
            'lstm_top_hits': lstm_top_hits,
            'lstm_combined': lstm_combined[:10],
            'lstm_combined_hit_details': lstm_combined_hit_details,
            'lstm_combined_hits': lstm_combined_hits,
            'excluded_numbers': excluded_list,
            'excluded_hit_details': excluded_hit_details,
            'excluded_hits': excluded_hits,
            'least_frequent_numbers': least_frequent_numbers,
            'least_frequent_hit_details': least_frequent_hit_details,
            'least_frequent_hits': least_frequent_hits,
            'combined_numbers': combined_numbers[:10],
            'combined_hit_details': combined_hit_details,
            'combined_hits': combined_hits,
            'lstm_hit_count': len(lstm_top_hits),
            'lstm_combined_hit_count': len(lstm_combined_hits),
            'excluded_hit_count': len(excluded_hits),
            'least_frequent_hit_count': len(least_frequent_hits),
            'combined_hit_count': len(combined_hits)
        }
    
    def test_date_range(self, start_date, end_date, min_periods=50):
        """Test từ ngày start_date đến end_date"""
        print(f"\n🎯 TESTING DATE RANGE: {start_date} → {end_date}")
        print(f"⚙️ Config: Minimum periods = {min_periods}")
        print("="*60)
        
        # Generate list of dates
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        
        test_dates = []
        current_date = start_dt
        while current_date <= end_dt:
            test_dates.append(current_date.strftime("%Y-%m-%d"))
            current_date += timedelta(days=1)
        
        # Thống kê tổng hợp
        total_stats = {
            'lstm_top_hits': [0] * 10,  # [top1_hits, top2_hits, ..., top10_hits]
            'excluded_top_hits': [0] * 10,
            'total_predictions': 0,
            'total_days': 0
        }
        
        # Thống kê theo ngày
        daily_stats = {}
        
        for test_date in test_dates:
            print(f"\n📅 Testing {test_date}...")
            
            day_draws = self.get_day_draws(test_date)
            if len(day_draws) < min_periods + 1:  # Cần ít nhất min_periods + 1 kì
                print(f"   ⚠️ Chỉ có {len(day_draws)} kì, cần ít nhất {min_periods + 1}, bỏ qua")
                continue

            # Thống kê ngày này
            day_stats = {
                'lstm_top_hits': [0] * 10,
                'excluded_top_hits': [0] * 10,
                'predictions_count': 0,
                'sample_lstm': None,
                'sample_excluded': None
            }

            # Test từ kì min_periods+1 đến cuối ngày (tối đa kì 119)
            max_period = min(len(day_draws), 119)
            print(f"   Testing periods {min_periods+1}-{max_period} ({max_period - min_periods} periods)")

            for period_index in range(min_periods, max_period):
                result = self.test_period(day_draws, period_index, min_periods)
                
                if result:
                    day_stats['predictions_count'] += 1
                    total_stats['total_predictions'] += 1

                    # Lưu sample predictions cho debug (lần đầu tiên)
                    if day_stats['sample_lstm'] is None:
                        day_stats['sample_lstm'] = result['lstm_10_predictions']
                        day_stats['sample_excluded'] = result['excluded_numbers']

                    # Cập nhật thống kê LSTM hits
                    for hit_pos in result['lstm_top_hits']:
                        if 1 <= hit_pos <= 10:
                            day_stats['lstm_top_hits'][hit_pos - 1] += 1
                            total_stats['lstm_top_hits'][hit_pos - 1] += 1

                    # Cập nhật thống kê excluded hits
                    for hit_pos in result['excluded_hits']:
                        if 1 <= hit_pos <= 10:
                            day_stats['excluded_top_hits'][hit_pos - 1] += 1
                            total_stats['excluded_top_hits'][hit_pos - 1] += 1

                    # Log chi tiết cho tất cả các kì
                    self._print_period_details(result)
            
            if day_stats['predictions_count'] > 0:
                daily_stats[test_date] = day_stats
                total_stats['total_days'] += 1
                print(f"   📊 Tổng: {day_stats['predictions_count']} predictions")
        
        # Hiển thị thống kê
        self._display_results(total_stats, daily_stats, start_date, end_date)
        
        return {
            'total_stats': total_stats,
            'daily_stats': daily_stats
        }

    def _print_period_details(self, result):
        """In chi tiết kết quả của một kì"""
        print(f"\n   🎯 KÌ {result['period']:3d} - {result['time']}")
        print(f"      📊 Kết quả thực tế: {result['actual_results']}")

        # 1. In 6 số dự đoán và kết quả
        print(f"      🤖 6 số dự đoán LSTM: {result['lstm_6_predictions']} → {result['lstm_6_hits']}/6 hits")

        # 2. In top1-10 LSTM và check hit
        print(f"      📈 Top 1-10 LSTM:")
        for detail in result['lstm_hit_details']:
            hit_mark = "✅" if detail['hit'] else "❌"
            print(f"         Top{detail['position']:2d}: {detail['number']:2d} {hit_mark}")

        # 2.1. In LSTM Combined (LSTM - Excluded) và check hit
        print(f"      🔗 LSTM Combined (LSTM - Excluded) Top 10:")
        for detail in result['lstm_combined_hit_details']:
            hit_mark = "✅" if detail['hit'] else "❌"
            print(f"         {detail['number']:2d} {hit_mark}")

        # 3. In excluded numbers và check hit
        if result['excluded_hit_details']:
            print(f"      🚫 Excluded numbers:")
            for detail in result['excluded_hit_details'][:10]:  # Chỉ hiển thị 10 số đầu
                hit_mark = "✅" if detail['hit'] else "❌"
                print(f"         {detail['number']:2d} {hit_mark}")
        else:
            print(f"      🚫 Excluded numbers: Không có")

        # 4. In least frequent numbers và check hit
        print(f"      📉 Top 10 ít xuất hiện nhất:")
        for detail in result['least_frequent_hit_details']:
            hit_mark = "✅" if detail['hit'] else "❌"
            print(f"         {detail['number']:2d} {hit_mark}")

        # 5. In combined numbers (least frequent - excluded) và check hit
        print(f"      🔗 Combined (Ít xuất hiện - Excluded) Top 10:")
        for detail in result['combined_hit_details']:
            hit_mark = "✅" if detail['hit'] else "❌"
            print(f"         {detail['number']:2d} {hit_mark}")

        print(f"      📊 Tổng kết:")
        print(f"         LSTM: {result['lstm_hit_count']}/10")
        print(f"         LSTM Combined: {result['lstm_combined_hit_count']}/10")
        print(f"         Excluded: {result['excluded_hit_count']}/{len(result['excluded_numbers'])}")
        print(f"         Least Frequent: {result['least_frequent_hit_count']}/10")
        print(f"         Combined: {result['combined_hit_count']}/10")

    def _display_results(self, total_stats, daily_stats, start_date, end_date):
        """Hiển thị kết quả thống kê"""
        print(f"\n📊 THỐNG KÊ TOP HIT THEO NGÀY")
        print("="*100)

        for test_date, day_stats in daily_stats.items():
            print(f"\n📅 {test_date} ({day_stats['predictions_count']} predictions):")
            print("-"*100)

            # LSTM top hits
            print("🤖 LSTM Top Hits:")
            lstm_rates = []
            for i in range(10):
                hit_count = day_stats['lstm_top_hits'][i]
                hit_rate = (hit_count / day_stats['predictions_count']) * 100 if day_stats['predictions_count'] > 0 else 0
                lstm_rates.append(f"Top{i+1}: {hit_rate:.1f}%")
            print("   " + " | ".join(lstm_rates))

            # Excluded top hits
            print("🚫 Excluded Top Hits:")
            excluded_rates = []
            for i in range(10):
                hit_count = day_stats['excluded_top_hits'][i]
                hit_rate = (hit_count / day_stats['predictions_count']) * 100 if day_stats['predictions_count'] > 0 else 0
                excluded_rates.append(f"Top{i+1}: {hit_rate:.1f}%")
            print("   " + " | ".join(excluded_rates))

            # Hiển thị sample predictions để debug
            if day_stats.get('sample_lstm') and day_stats.get('sample_excluded'):
                print(f"   📝 Sample LSTM: {day_stats['sample_lstm']}")
                print(f"   📝 Sample Excluded: {day_stats['sample_excluded']}")
            elif day_stats.get('sample_lstm'):
                print(f"   📝 Sample LSTM: {day_stats['sample_lstm']}")
                print(f"   📝 Sample Excluded: []")
            else:
                print(f"   📝 No sample data available")
        
        # Thống kê tổng hợp
        print(f"\n🎯 THỐNG KÊ TỔNG HỢP ({start_date} → {end_date})")
        print("="*100)
        print(f"Tổng: {total_stats['total_days']} ngày, {total_stats['total_predictions']} predictions")
        
        print("\n🤖 LSTM Average Top Hit Rates:")
        lstm_avg_rates = []
        for i in range(10):
            hit_count = total_stats['lstm_top_hits'][i]
            hit_rate = (hit_count / total_stats['total_predictions']) * 100 if total_stats['total_predictions'] > 0 else 0
            lstm_avg_rates.append(f"Top{i+1}: {hit_rate:.1f}%")
        print("   " + " | ".join(lstm_avg_rates))
        
        print("\n🚫 Excluded Average Top Hit Rates:")
        excluded_avg_rates = []
        for i in range(10):
            hit_count = total_stats['excluded_top_hits'][i]
            hit_rate = (hit_count / total_stats['total_predictions']) * 100 if total_stats['total_predictions'] > 0 else 0
            excluded_avg_rates.append(f"Top{i+1}: {hit_rate:.1f}%")
        print("   " + " | ".join(excluded_avg_rates))
        
        print("="*100)

def test_range(start_date, end_date, min_periods=50):
    """Function để test từ command line"""
    predictor = SimplifiedKenoPredictor()
    return predictor.test_date_range(start_date, end_date, min_periods)

if __name__ == "__main__":
    import sys

    if len(sys.argv) < 3 or len(sys.argv) > 4:
        print("Usage: python simplified_predictor.py START_DATE END_DATE [MIN_PERIODS]")
        print("Example: python simplified_keno_predictor.py 2025-04-01 2025-04-30")
        print("Example: python simplified_predictor.py 2025-04-01 2025-04-30 30")
        sys.exit(1)

    start_date = sys.argv[1]
    end_date = sys.argv[2]
    min_periods = int(sys.argv[3]) if len(sys.argv) == 4 else 50

    test_range(start_date, end_date, min_periods)
