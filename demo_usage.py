#!/usr/bin/env python3
"""
Demo sử dụng Keno AI Predictor - Simple Date Range Tester
"""

print("🎯 DEMO USAGE - KENO AI PREDICTOR")
print("="*50)
print()
print("📋 CÁC CÁCH SỬ DỤNG:")
print()
print("1️⃣ COMMAND LINE:")
print("   python3 keno_ai_predictor.py test 2025-04-01 2025-04-05")
print("   python3 keno_ai_predictor.py test 2025-01-01 2025-01-31")
print()
print("2️⃣ INTERACTIVE MODE:")
print("   python3 keno_ai_predictor.py")
print("   # Sau đó nhập start_date và end_date")
print()
print("📊 KẾT QUẢ MONG ĐỢI:")
print("   - Chỉ hiển thị kết quả 4/4 đúng")
print("   - Format: Ngày - Kì - Giờ")
print("   - <PERSON><PERSON> dự đoán vs <PERSON><PERSON> thực tế")
print("   - Tổng kết cuối: tỷ lệ thành công")
print()
print("🎯 VÍ DỤ OUTPUT:")
print("   📅 2025-04-01 - Period 52 (10:30:00)")
print("   🎯 Predicted: [12, 25, 43, 67]")
print("   ✅ Actual Missing: [12, 25, 43, 67]")
print("   🎉 Result: PERFECT 4/4 MATCH!")
print()
print("="*50)
print("💡 Lưu ý: Cần có dữ liệu trong database để test!")
