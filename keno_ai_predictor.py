#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 KENO AI PREDICTOR - DATE RANGE TESTING TOOL
===============================================

Tool for testing Keno AI prediction accuracy over a specified date range.
Focuses on logging 4/4 correct predictions.

Core Prediction Logic Features (handled by SimplifiedKenoPredictor):
- Deep Historical Analysis
- Consecutive Number Exclusion
- Seasonal Pattern Recognition

Usage:
python keno_ai_predictor.py <start_date> <end_date>
Example:
python keno_ai_predictor.py 2025-01-01 2025-01-10

Author: Keno AI Team
Version: 5.1 - Date Range Testing Tool
"""

import sys
import os
import argparse
from datetime import datetime, timedelta
from io import StringIO

# Import từ file gốc
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from simplified_keno_predictor import SimplifiedKenoPredictor

class KenoAIPredictor:
    """Keno AI Predictor - Date Range Testing Tool"""

    def __init__(self, analysis_periods=5000, consecutive_enable=True, consecutive_lookback=3, consecutive_threshold=2):
        self.predictor = SimplifiedKenoPredictor()
        self.version = "5.1 - Date Range Testing Tool" # Updated version

        # Cấu hình phân tích - có thể thay đổi (5000, 10000, 15000...)
        self.analysis_periods = analysis_periods

        # ⚙️ CẤU HÌNH CONSECUTIVE NUMBER EXCLUSION
        self.consecutive_lookback_periods = consecutive_lookback    # Số kì gần nhất để kiểm tra
        self.consecutive_threshold = consecutive_threshold          # Số lần xuất hiện tối thiểu để loại bỏ
        self.consecutive_enable = consecutive_enable               # Bật/tắt tính năng Consecutive Number Exclusion
        self.consecutive_reinclusion_periods = 3                  # Số kì để xem xét đưa số bị loại quay lại (mặc định: 5)

        # Theo dõi số bị loại bỏ và thời gian loại bỏ
        self.excluded_numbers_history = {}  # {number: period_excluded}

        print(f"📊 Cấu hình: Sử dụng {self.analysis_periods} periods để phân tích")
        print(f"🔧 Consecutive Number Exclusion: {'BẬT' if self.consecutive_enable else 'TẮT'}")
        if self.consecutive_enable:
            print(f"   📝 Kiểm tra {self.consecutive_lookback_periods} kì gần nhất, loại bỏ số xuất hiện >= {self.consecutive_threshold} lần")

    # predict_now, _display_martingale_recommendation, test_accuracy,
    # analyze_best_times, quick_stats, export_predictions are removed.
    # test_date_range is kept.

    def get_historical_data_before_date(self, before_date, limit=None):
        """Lấy dữ liệu lịch sử trước ngày cụ thể để phân tích

        Args:
            before_date (str): Ngày giới hạn (YYYY-MM-DD)
            limit (int): Số lượng records tối đa (mặc định dùng self.analysis_periods)

        Returns:
            list: Danh sách kết quả các kì
        """
        if limit is None:
            limit = self.analysis_periods

        try:
            # Sử dụng method từ predictor để lấy data
            from variable_length_model import connect_db

            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            query = """
                SELECT results, date, time, period
                FROM histories_keno
                WHERE date < %s
                ORDER BY date DESC, time DESC
                LIMIT %s
            """

            cursor.execute(query, (before_date, limit))
            rows = cursor.fetchall()

            cursor.close()
            conn.close()

            # Chuyển đổi results thành list và đảo ngược để có thứ tự thời gian tăng dần
            historical_data = []
            for row in reversed(rows):  # Đảo ngược để có thứ tự cũ -> mới
                row['results'] = [int(n) for n in row['results'].split(',')]
                historical_data.append(row['results'])

            print(f"📈 Đã tải {len(historical_data)} records lịch sử trước ngày {before_date}")
            return historical_data

        except Exception as e:
            print(f"❌ Lỗi khi lấy dữ liệu lịch sử: {e}")
            return []



    def _get_consecutive_excluded_numbers(self, recent_draws, current_period=None):
        """Lấy danh sách số bị loại bỏ do xuất hiện liên tục với logic re-inclusion

        Sử dụng các biến cấu hình:
        - self.consecutive_enable: Bật/tắt tính năng
        - self.consecutive_lookback_periods: Số kì gần nhất để kiểm tra
        - self.consecutive_threshold: Số lần xuất hiện tối thiểu để loại bỏ
        - self.consecutive_reinclusion_periods: Số kì để xem xét đưa số bị loại quay lại
        """
        # Kiểm tra xem tính năng có được bật không
        if not self.consecutive_enable:
            return set(), set()

        # Kiểm tra đủ dữ liệu
        if len(recent_draws) < self.consecutive_lookback_periods:
            return set(), set()

        # Đếm số lần xuất hiện của mỗi số trong N kì gần nhất (theo cấu hình)
        number_counts = {}
        for draw in recent_draws[-self.consecutive_lookback_periods:]:
            for num in draw:
                number_counts[num] = number_counts.get(num, 0) + 1

        # Loại bỏ số xuất hiện >= threshold lần (theo cấu hình)
        newly_excluded = {num for num, count in number_counts.items() if count >= self.consecutive_threshold}

        # Cập nhật lịch sử loại bỏ
        if current_period is not None:
            for num in newly_excluded:
                if num not in self.excluded_numbers_history:
                    self.excluded_numbers_history[num] = current_period

        # Kiểm tra số nào có thể được đưa quay lại (re-inclusion)
        reincluded_numbers = set()
        if current_period is not None:
            numbers_to_remove = []
            for num, excluded_period in self.excluded_numbers_history.items():
                periods_excluded = current_period - excluded_period
                if periods_excluded >= self.consecutive_reinclusion_periods:
                    # Kiểm tra xem số này có còn xuất hiện liên tục không
                    if num not in newly_excluded:
                        reincluded_numbers.add(num)
                        numbers_to_remove.append(num)

            # Xóa số đã được đưa quay lại khỏi lịch sử
            for num in numbers_to_remove:
                del self.excluded_numbers_history[num]

        return newly_excluded, reincluded_numbers

    def _get_seasonal_pattern_info(self, combined_draws, period_number):
        """Lấy thông tin về Seasonal Pattern Recognition"""
        try:
            # Phân tích theo khung giờ và pattern lịch sử
            if period_number <= 60:
                time_period = "Sáng"
                pattern = "Ưu tiên số lẻ, tránh số chẵn liên tiếp"
            elif period_number <= 90:
                time_period = "Chiều"
                pattern = "Cân bằng số chẵn/lẻ, ưu tiên số trung bình"
            else:
                time_period = "Tối"
                pattern = "Ưu tiên số cao, tránh số thấp liên tiếp"

            # Phân tích trend từ dữ liệu gần đây
            if len(combined_draws) >= 20:
                recent_avg = sum(sum(draw) / len(draw) for draw in combined_draws[-20:]) / 20
                trend = f", Trend TB: {recent_avg:.1f}"
            else:
                trend = ""

            return f"{time_period} - {pattern}{trend}"
        except:
            return "Đang phân tích pattern theo thời gian"

    def _get_deep_historical_top_10(self, combined_draws):
        """Lấy top 10 số từ Deep Historical Analysis"""
        try:
            # Phân tích tần suất trượt trong lịch sử
            missing_freq = {}
            total_draws = len(combined_draws)

            for num in range(1, 81):
                missing_count = 0
                for draw in combined_draws:
                    if num not in draw:
                        missing_count += 1
                missing_freq[num] = missing_count / total_draws if total_draws > 0 else 0

            # Sắp xếp theo tần suất trượt giảm dần và lấy top 10
            sorted_numbers = sorted(missing_freq.items(), key=lambda x: x[1], reverse=True)
            top_10 = [num for num, _ in sorted_numbers[:10]]

            return top_10
        except:
            return list(range(1, 11))  # Fallback

    def _get_seasonal_pattern_top_10(self, combined_draws, period_number):
        """Lấy top 10 số từ Seasonal Pattern Recognition"""
        try:
            # Phân tích theo khung giờ
            if period_number <= 60:
                # Sáng: Ưu tiên số lẻ
                candidates = [num for num in range(1, 81) if num % 2 == 1]
            elif period_number <= 90:
                # Chiều: Ưu tiên số trung bình (30-50)
                candidates = list(range(30, 51))
            else:
                # Tối: Ưu tiên số cao (60-80)
                candidates = list(range(60, 81))

            # Phân tích tần suất trượt của candidates trong dữ liệu gần đây
            recent_draws = combined_draws[-50:] if len(combined_draws) >= 50 else combined_draws
            missing_freq = {}

            for num in candidates:
                missing_count = 0
                for draw in recent_draws:
                    if num not in draw:
                        missing_count += 1
                missing_freq[num] = missing_count / len(recent_draws) if recent_draws else 0

            # Sắp xếp và lấy top 10
            sorted_candidates = sorted(missing_freq.items(), key=lambda x: x[1], reverse=True)
            top_10 = [num for num, _ in sorted_candidates[:10]]

            # Đảm bảo có đủ 10 số
            if len(top_10) < 10:
                remaining = [num for num in range(1, 81) if num not in top_10]
                top_10.extend(remaining[:10-len(top_10)])

            return top_10[:10]
        except:
            return list(range(1, 11))  # Fallback

    def _analyze_feature_hit_rate(self, feature_name, top_10_numbers, actual_missing):
        """Phân tích hit rate của feature với actual missing numbers"""
        try:
            hits = []
            for i, num in enumerate(top_10_numbers, 1):
                hit = "✅ YES" if num in actual_missing else "❌ NO"
                hits.append(f"Top{i}: {hit}")

            # Đếm số hit
            hit_count = sum(1 for num in top_10_numbers if num in actual_missing)
            hit_rate = (hit_count / 10) * 100

            print(f"         🎯 Hit Analysis: {' | '.join(hits[:5])}")
            print(f"         📊 Hit Rate: {hit_count}/10 ({hit_rate:.1f}%)")

            # Cập nhật thống kê tổng hợp (nếu có)
            if not hasattr(self, 'feature_hit_stats'):
                self.feature_hit_stats = {}

            if feature_name not in self.feature_hit_stats:
                self.feature_hit_stats[feature_name] = {'total_predictions': 0, 'total_hits': 0}

            self.feature_hit_stats[feature_name]['total_predictions'] += 10
            self.feature_hit_stats[feature_name]['total_hits'] += hit_count

        except Exception as e:
            print(f"         ⚠️ Lỗi phân tích hit rate: {e}")

    def _analyze_day_category_hit_rate(self, category_name, numbers, actual_missing):
        """Phân tích hit rate cho từng loại số trong ngày"""
        try:
            if not numbers:
                return

            hit_count = sum(1 for num in numbers if num in actual_missing)
            hit_rate = (hit_count / len(numbers)) * 100

            # Cập nhật thống kê tổng hợp
            if not hasattr(self, 'day_category_hit_stats'):
                self.day_category_hit_stats = {}

            if category_name not in self.day_category_hit_stats:
                self.day_category_hit_stats[category_name] = {'total_predictions': 0, 'total_hits': 0}

            self.day_category_hit_stats[category_name]['total_predictions'] += len(numbers)
            self.day_category_hit_stats[category_name]['total_hits'] += hit_count

            # Log gọn
            print(f"         🎯 {category_name}: {hit_count}/{len(numbers)} ({hit_rate:.1f}%)")

        except Exception as e:
            print(f"         ⚠️ Lỗi phân tích {category_name}: {e}")

    def _analyze_current_day_numbers(self, current_day_draws, test_date, actual_missing=None):
        """Phân tích số xuất hiện trong ngày hiện tại với đầy đủ thống kê

        Args:
            current_day_draws: Danh sách kết quả các kì trong ngày hiện tại
            test_date: Ngày đang test
            actual_missing: Số thực tế bị trượt (để tính hit rate)

        Returns:
            dict: Thông tin về số xuất hiện với đầy đủ phân tích
        """
        if not current_day_draws:
            return {'top_5_candidates': []}

        # Đếm số lần xuất hiện của mỗi số trong ngày hiện tại
        number_counts = {}
        for num in range(1, 81):
            count = 0
            for draw in current_day_draws:
                if num in draw:
                    count += 1
            number_counts[num] = count

        # Phân loại và sắp xếp
        missing_numbers = sorted([num for num, count in number_counts.items() if count == 0])

        # Top 10 số xuất hiện nhiều nhất
        most_frequent = sorted(number_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        top_10_most = [num for num, _ in most_frequent]

        # Top 10 số xuất hiện ít nhất (loại trừ số không xuất hiện)
        least_frequent = sorted([(num, count) for num, count in number_counts.items() if count > 0],
                               key=lambda x: x[1])[:10]
        top_10_least = [num for num, _ in least_frequent]

        # Top 10 số xuất hiện 5-10 lần
        moderate_frequent = sorted([(num, count) for num, count in number_counts.items() if 5 <= count <= 10],
                                  key=lambda x: x[1], reverse=True)[:10]
        top_10_moderate = [num for num, _ in moderate_frequent]

        # Logs gọn
        print(f"      📈 Phân tích ngày {test_date} (từ {len(current_day_draws)} kì):")
        print(f"         � Top 10 nhiều nhất: {top_10_most}")
        print(f"         ❄️  Top 10 ít nhất: {top_10_least}")
        print(f"         ⚖️  Top 10 (5-10 lần): {top_10_moderate}")
        print(f"         🚫 Chưa xuất hiện ({len(missing_numbers)}): {missing_numbers[:10]}{'...' if len(missing_numbers) > 10 else ''}")

        # Phân tích hit rate cho từng loại
        if actual_missing is not None:
            self._analyze_day_category_hit_rate("Nhiều nhất", top_10_most, actual_missing)
            self._analyze_day_category_hit_rate("Ít nhất", top_10_least, actual_missing)
            self._analyze_day_category_hit_rate("5-10 lần", top_10_moderate, actual_missing)
            self._analyze_day_category_hit_rate("Chưa xuất hiện", missing_numbers[:10], actual_missing)

        # Kết hợp để tạo top 5 candidates
        candidates = missing_numbers + top_10_moderate + top_10_least
        unique_candidates = list(dict.fromkeys(candidates))[:5]

        return {
            'top_5_candidates': unique_candidates,
            'top_10_most': top_10_most,
            'top_10_least': top_10_least,
            'top_10_moderate': top_10_moderate,
            'missing_numbers': missing_numbers,
            'number_counts': number_counts
        }

    def _combine_all_features_for_5_numbers(self, deep_top_10, seasonal_top_10, day_analysis, actual_missing=None):
        """Kết hợp tất cả features để tạo dự đoán 5 số

        Args:
            deep_top_10: Top 10 từ Deep Historical Analysis
            seasonal_top_10: Top 10 từ Seasonal Pattern
            day_analysis: Kết quả phân tích ngày hiện tại
            actual_missing: Số thực tế bị trượt (để tính hit rate)

        Returns:
            list: 5 số cuối cùng
        """
        print(f"      🔄 LOGS TOP 10 TẤT CẢ FEATURES:")

        # 1. Logs top 10 của tất cả features
        print(f"         🧠 Deep Historical Top 10: {deep_top_10}")
        print(f"         🌟 Seasonal Pattern Top 10: {seasonal_top_10}")

        # Lấy top 10 từ day analysis
        day_least = day_analysis.get('top_10_least', [])[:10]
        day_missing = day_analysis.get('missing_numbers', [])[:10]
        print(f"         ❄️  Day Least Top 10: {day_least}")
        print(f"         🚫 Day Missing Top 10: {day_missing}")

        # 2. Kiểm tra hit rate của top 10 số
        if actual_missing is not None:
            print(f"      🎯 HIT RATE ANALYSIS:")
            deep_hits = sum(1 for num in deep_top_10 if num in actual_missing)
            seasonal_hits = sum(1 for num in seasonal_top_10 if num in actual_missing)
            least_hits = sum(1 for num in day_least if num in actual_missing)
            missing_hits = sum(1 for num in day_missing if num in actual_missing)

            print(f"         🧠 Deep Historical: {deep_hits}/10 ({deep_hits*10:.0f}%)")
            print(f"         🌟 Seasonal Pattern: {seasonal_hits}/10 ({seasonal_hits*10:.0f}%)")
            print(f"         ❄️  Day Least: {least_hits}/10 ({least_hits*10:.0f}%)")
            print(f"         🚫 Day Missing: {missing_hits}/10 ({missing_hits*10:.0f}%)")

            # Cập nhật thống kê hit rate
            self._analyze_feature_hit_rate("Deep Historical", deep_top_10, actual_missing)
            self._analyze_feature_hit_rate("Seasonal Pattern", seasonal_top_10, actual_missing)
            self._analyze_day_category_hit_rate("Day Least", day_least, actual_missing)
            self._analyze_day_category_hit_rate("Day Missing", day_missing, actual_missing)

        # 3. Tạo dự đoán 5 số theo logic mới
        print(f"      🎯 TẠO DỰ ĐOÁN 5 SỐ:")
        final_5_numbers = []

        # Top 1,2 từ Deep Historical
        if len(deep_top_10) >= 2:
            final_5_numbers.extend(deep_top_10[:2])
            print(f"         ✅ Top 1,2 Deep: {deep_top_10[:2]}")
        elif len(deep_top_10) >= 1:
            final_5_numbers.extend(deep_top_10[:1])
            print(f"         ✅ Top 1 Deep: {deep_top_10[:1]}")

        # Top 1,2 từ Seasonal
        for num in seasonal_top_10[:2]:
            if num not in final_5_numbers and len(final_5_numbers) < 5:
                final_5_numbers.append(num)
        if len([num for num in seasonal_top_10[:2] if num not in deep_top_10[:2]]) > 0:
            added_seasonal = [num for num in seasonal_top_10[:2] if num not in deep_top_10[:2]]
            print(f"         ✅ Top 1,2 Seasonal: {added_seasonal}")

        # 1 số ít nhất hoặc chưa ra
        day_candidates = day_missing + day_least
        for num in day_candidates:
            if num not in final_5_numbers and len(final_5_numbers) < 5:
                final_5_numbers.append(num)
                print(f"         ✅ Day candidate: {num}")
                break

        # Đảm bảo có đúng 5 số
        final_5_numbers = final_5_numbers[:5]

        print(f"         🎯 KẾT QUẢ 5 SỐ: {final_5_numbers}")
        print(f"         📊 Logic: Top1,2_Deep + Top1,2_Seasonal + 1_Day_Rare")

        return final_5_numbers

    def _display_feature_hit_stats(self):
        """Hiển thị thống kê tổng hợp hit rate của các features và day categories"""
        # Features hit stats
        if hasattr(self, 'feature_hit_stats') and self.feature_hit_stats:
            print(f"\n🎯 THỐNG KÊ HIT RATE CÁC FEATURES")
            print("="*60)
            print(f"{'Feature':<20} {'Predictions':<12} {'Hits':<8} {'Hit Rate':<10}")
            print("-"*60)

            for feature_name, stats in self.feature_hit_stats.items():
                total_predictions = stats['total_predictions']
                total_hits = stats['total_hits']
                hit_rate = (total_hits / total_predictions * 100) if total_predictions > 0 else 0

                print(f"{feature_name:<20} {total_predictions:<12} {total_hits:<8} {hit_rate:>6.1f}%")

            print("="*60)

        # Day categories hit stats
        if hasattr(self, 'day_category_hit_stats') and self.day_category_hit_stats:
            print(f"\n📈 THỐNG KÊ HIT RATE CÁC LOẠI SỐ TRONG NGÀY")
            print("="*60)
            print(f"{'Loại số':<20} {'Predictions':<12} {'Hits':<8} {'Hit Rate':<10}")
            print("-"*60)

            for category_name, stats in self.day_category_hit_stats.items():
                total_predictions = stats['total_predictions']
                total_hits = stats['total_hits']
                hit_rate = (total_hits / total_predictions * 100) if total_predictions > 0 else 0

                print(f"{category_name:<20} {total_predictions:<12} {total_hits:<8} {hit_rate:>6.1f}%")

            print("="*60)

    def test_date_range(self, start_date, end_date):
        """Test độ chính xác của AI trong khoảng thời gian cụ thể - DỰ ĐOÁN TẤT CẢ CÁC KÌ

        Args:
            start_date (str): Ngày bắt đầu (YYYY-MM-DD)
            end_date (str): Ngày kết thúc (YYYY-MM-DD)
        """
        print(f"\n🎯 KIỂM TRA DỰ ĐOÁN AI - TẤT CẢ CÁC KÌ TỪ 51-119")
        print("="*60)
        print(f"📅 Khoảng thời gian: {start_date} đến {end_date}")
        print("🔍 Hiển thị tất cả dự đoán và kết quả thực tế")
        print("="*60)

        try:
            # Validate date format
            start_dt = datetime.strptime(start_date, "%Y-%m-%d")
            end_dt = datetime.strptime(end_date, "%Y-%m-%d")

            if start_dt >= end_dt:
                print("❌ Ngày bắt đầu phải trước ngày kết thúc")
                return None

        except ValueError:
            print("❌ Định dạng ngày không hợp lệ. Sử dụng YYYY-MM-DD")
            return None

        # Generate list of dates to test
        test_dates = []
        current_date = start_dt
        while current_date <= end_dt:
            test_dates.append(current_date.strftime("%Y-%m-%d"))
            current_date += timedelta(days=1)

        total_predictions = 0
        total_perfect = 0
        perfect_predictions_log = []
        daily_stats = []  # Thống kê từng ngày

        # Lấy dữ liệu lịch sử trước ngày bắt đầu test để phân tích
        print(f"🔍 Đang tải {self.analysis_periods} records lịch sử trước ngày {start_date}...")
        historical_data = self.get_historical_data_before_date(start_date, self.analysis_periods)

        if len(historical_data) < 1000:  # Cần ít nhất 1000 records để phân tích
            print(f"⚠️ Chỉ có {len(historical_data)} records lịch sử, cần ít nhất 1000 records")
            print("💡 Hãy thử với ngày bắt đầu muộn hơn hoặc giảm analysis_periods")
            return None

        for i, test_date in enumerate(test_dates, 1):
            print(f"\n[{i}/{len(test_dates)}] Đang kiểm tra ngày {test_date}...")

            day_draws = self.predictor.get_day_draws(test_date)
            if len(day_draws) < 51:
                print(f"   ⚠️ Không đủ dữ liệu ({len(day_draws)} kì)")
                continue

            day_perfect = 0
            day_total = 0

            # Test từ kì 51 đến hết ngày (tối đa 120 kì)
            max_periods = min(len(day_draws), 120)  # Giới hạn tối đa 120 kì/ngày

            print(f"   📊 Dự đoán từ kì 51 đến kì {max_periods} (tổng {max_periods - 50} dự đoán)")
            print(f"   " + "="*70)

            for draw_index in range(50, max_periods):
                period_number = draw_index + 1

                # Kết hợp dữ liệu lịch sử với dữ liệu ngày hiện tại
                current_day_draws = [day_draws[j]['results'] for j in range(draw_index)]
                combined_draws = historical_data + current_day_draws

                # Chỉ lấy số lượng data cần thiết (tránh quá nhiều data)
                if len(combined_draws) > self.analysis_periods + 200:
                    combined_draws = combined_draws[-(self.analysis_periods + 200):]

                try:
                    # Hiển thị quá trình phân tích chi tiết
                    print(f"      🔍 Phân tích kì {period_number} với {len(combined_draws)} records...")

                    # Capture output để lấy thông tin chi tiết
                    old_stdout = sys.stdout
                    captured_output = StringIO()
                    sys.stdout = captured_output

                    five_numbers, combinations = self.predictor.predict_statistical(day_results=combined_draws)

                    sys.stdout = old_stdout

                    # Phân tích output để lấy thông tin về Deep Historical Analysis
                    output_text = captured_output.getvalue()

                    # Khởi tạo final_prediction mặc định
                    final_prediction = None

                    # Lấy actual missing numbers để phân tích hit rate (đặt trước để tránh lỗi)
                    if draw_index < len(day_draws):
                        actual_results = day_draws[draw_index]['results']
                        actual_missing_for_analysis = set(range(1, 81)) - set(actual_results)
                    else:
                        actual_missing_for_analysis = None

                    # Tìm thông tin về 6 số từ Deep Historical Analysis
                    if "STATISTICAL KENO PREDICTION" in output_text:
                        # Hiển thị quá trình kết hợp features (gọn)
                        if five_numbers:
                            # Lấy top 1 và top 4,5 từ Deep Historical Analysis
                            top_1_deep = five_numbers[0] if len(five_numbers) >= 1 else None
                            top_4_5_deep = five_numbers[3:5] if len(five_numbers) >= 5 else five_numbers[3:] if len(five_numbers) > 3 else []

                            print(f"      🧠 Deep Analysis: {five_numbers} | Top1: {top_1_deep} | Top4,5: {top_4_5_deep}")

                            # Phân tích số xuất hiện ít/không xuất hiện trong ngày hiện tại
                            day_analysis_result = self._analyze_current_day_numbers(current_day_draws, test_date, actual_missing_for_analysis)

                            # Lấy top 10 từ các features
                            deep_top_10 = self._get_deep_historical_top_10(combined_draws)
                            seasonal_top_10 = self._get_seasonal_pattern_top_10(combined_draws, period_number)

                            # Kết hợp tất cả features để tạo dự đoán 5 số
                            final_prediction = self._combine_all_features_for_5_numbers(
                                deep_top_10, seasonal_top_10, day_analysis_result, actual_missing_for_analysis
                            )

                            if final_prediction and len(final_prediction) >= 5:
                                print(f"      🎯 Kết hợp tất cả features → 5 số cuối: {final_prediction}")
                            else:
                                print(f"      ⚠️ Không thể tạo dự đoán 5 số")
                        else:
                            print(f"      ❌ Deep Analysis không tạo ra 6 số")

                    if five_numbers:
                        actual_results = day_draws[draw_index]['results']
                        # Sử dụng final_prediction (5 số) nếu có, nếu không thì dùng five_numbers
                        if final_prediction and len(final_prediction) == 5:
                            predicted_5 = final_prediction
                        else:
                            predicted_5 = five_numbers[:5] if len(five_numbers) >= 5 else five_numbers
                        actual_missing = set(range(1, 81)) - set(actual_results)
                        correct_count = len(set(predicted_5) & actual_missing)

                        # Lấy thông tin period
                        period_info = day_draws[draw_index]
                        period_time = period_info.get('time', 'Unknown')
                        period_name = period_info.get('period', f"P{period_number}")

                        day_total += 1

                        # In kết quả chi tiết cho mỗi kì
                        status_icon = "🎯" if correct_count == 5 else "🔸" if correct_count >= 4 else "⚪"
                        print(f"      📋 KẾT QUẢ DỰ ĐOÁN:")
                        print(f"      🔮 Dự đoán cho kì {period_number}: {predicted_5}")
                        print(f"      {status_icon} {test_date} Kì {period_number:3d} ({period_time}) - {period_name}")
                        print(f"      🎲 Kết quả kì {period_number}: {actual_results}")
                        print(f"      ✅ Độ chính xác: {correct_count}/5 đúng")

                        if correct_count == 5:
                            day_perfect += 1
                            print(f"      🏆 HOÀN HẢO 5/5! Tất cả số dự đoán đều trượt!")

                            # LOG PERFECT PREDICTION
                            perfect_predictions_log.append({
                                'date': test_date,
                                'period': period_name,
                                'time': period_time,
                                'predicted_5': predicted_5,
                                'actual_results': actual_results,
                                'actual_missing': sorted(list(actual_missing)),
                                'correct_numbers': sorted(list(set(predicted_5) & actual_missing))
                            })

                        print(f"      " + "="*70)
                    else:
                        print(f"   ⚠️ {test_date} Kì {period_number:3d}: Không thể tạo dự đoán (five_numbers={bool(five_numbers)}, combinations={len(combinations) if combinations else 0})")

                except Exception as e:
                    print(f"   ❌ {test_date} Lỗi kì {period_number}: {str(e)[:50]}...")
                    continue

            if day_total > 0:
                day_rate = (day_perfect / day_total) * 100
                if day_perfect > 0:
                    print(f"   📊 Kết quả ngày: {day_perfect}/{day_total} hoàn hảo ({day_rate:.1f}%) - Tìm thấy {day_perfect} dự đoán HOÀN HẢO!")
                else:
                    print(f"   📊 Kết quả ngày: {day_perfect}/{day_total} hoàn hảo ({day_rate:.1f}%)")

                # Lưu thống kê ngày
                daily_stats.append({
                    'date': test_date,
                    'total_predictions': day_total,
                    'perfect_predictions': day_perfect,
                    'accuracy_rate': day_rate,
                    'periods_tested': f"{51}-{max_periods}"
                })

                total_predictions += day_total
                total_perfect += day_perfect
            else:
                print(f"   ⚠️ Không có dự đoán nào được thực hiện cho ngày này")
                # Lưu thống kê ngày với 0 predictions
                daily_stats.append({
                    'date': test_date,
                    'total_predictions': 0,
                    'perfect_predictions': 0,
                    'accuracy_rate': 0,
                    'periods_tested': 'N/A'
                })

        # Tính toán overall_rate cho return value
        if total_predictions > 0:
            overall_rate = (total_perfect / total_predictions) * 100
        else:
            overall_rate = 0

        # Hiển thị thống kê hit rate của các features
        self._display_feature_hit_stats()

        # Hiển thị thống kê từng ngày
        if daily_stats:
            print(f"\n📊 THỐNG KÊ XÁC SUẤT TỪNG NGÀY")
            print("="*80)
            print(f"{'Ngày':<12} {'Kì Test':<10} {'Tổng':<6} {'5/5':<6} {'Tỷ lệ':<8} {'Đánh giá':<15}")
            print("-"*80)

            for stat in daily_stats:
                date = stat['date']
                periods = stat['periods_tested']
                total = stat['total_predictions']
                perfect = stat['perfect_predictions']
                rate = stat['accuracy_rate']

                # Đánh giá hiệu suất
                if rate >= 35:
                    performance = "🔥 XUẤT SẮC"
                elif rate >= 30:
                    performance = "✅ TỐT"
                elif rate >= 25:
                    performance = "🔸 KHẢ QUAN"
                elif rate >= 15:
                    performance = "⚪ TRUNG BÌNH"
                elif rate > 0:
                    performance = "⚠️ THẤP"
                else:
                    performance = "❌ KHÔNG CÓ"

                print(f"{date:<12} {periods:<10} {total:<6} {perfect:<6} {rate:>6.1f}% {performance:<15}")

            print("-"*80)
            print(f"{'TỔNG CỘNG':<12} {'':<10} {total_predictions:<6} {total_perfect:<6} {overall_rate:>6.1f}% {'TRUNG BÌNH':<15}")
            print("="*80)

        return {
            'start_date': start_date,
            'end_date': end_date,
            'total_predictions': total_predictions,
            'total_perfect': total_perfect,
            'accuracy_rate': overall_rate if total_predictions > 0 else 0,
            'perfect_predictions': perfect_predictions_log
        }

# Removed test_accuracy, analyze_best_times, quick_stats, export_predictions methods from class
# Removed global helper functions: test_date_range_simple, predict_now_simple, show_welcome, command_line_interface, main

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="🎯 KENO AI PREDICTOR - CÔNG CỤ KIỂM TRA KHOẢNG THỜI GIAN\n"
                    "Công cụ kiểm tra độ chính xác dự đoán Keno AI trong khoảng thời gian cụ thể.\n"
                    "Hiển thị tất cả dự đoán từ kì 51-119 mỗi ngày.",
        formatter_class=argparse.RawTextHelpFormatter, # Allows for newlines in description
        epilog="Ví dụ sử dụng:\n"
               "  # Sử dụng cơ bản:\n"
               "  python keno_ai_predictor.py 2025-04-01 2025-04-30\n"
               "  \n"
               "  # Tùy chỉnh số periods phân tích:\n"
               "  python keno_ai_predictor.py 2025-04-01 2025-04-30 --analysis-periods 10000\n"
               "  \n"
               "  # Tắt Consecutive Number Exclusion:\n"
               "  python keno_ai_predictor.py 2025-04-01 2025-04-30 --consecutive-enable False\n"
               "  \n"
               "  # Tùy chỉnh Consecutive Number Exclusion:\n"
               "  python keno_ai_predictor.py 2025-04-01 2025-04-30 --consecutive-lookback 5 --consecutive-threshold 3\n"
               "  \n"
               "  # Kết hợp nhiều tùy chỉnh:\n"
               "  python keno_ai_predictor.py 2025-04-01 2025-04-30 --analysis-periods 15000 --consecutive-lookback 4 --consecutive-threshold 2"
    )
    parser.add_argument("start_date", type=str, help="Ngày bắt đầu kiểm tra (định dạng YYYY-MM-DD)")
    parser.add_argument("end_date", type=str, help="Ngày kết thúc kiểm tra (định dạng YYYY-MM-DD)")
    parser.add_argument("--analysis-periods", type=int, default=5000,
                       help="Số lượng periods lịch sử để phân tích (mặc định: 5000, có thể dùng 10000, 15000...)")

    # ⚙️ Cấu hình Consecutive Number Exclusion
    parser.add_argument("--consecutive-enable", type=bool, default=True,
                       help="Bật/tắt tính năng Consecutive Number Exclusion (mặc định: True)")
    parser.add_argument("--consecutive-lookback", type=int, default=3,
                       help="Số kì gần nhất để kiểm tra consecutive numbers (mặc định: 3)")
    parser.add_argument("--consecutive-threshold", type=int, default=2,
                       help="Số lần xuất hiện tối thiểu để loại bỏ số (mặc định: 2)")

    # Check if any arguments were passed (sys.argv includes script name as first element)
    if len(sys.argv) <= 1:
        parser.print_help(sys.stderr)
        sys.exit(1) # Exit if no arguments are provided

    args = parser.parse_args()

    print(f"🚀 Khởi động Keno AI Predictor - Chế độ kiểm tra khoảng thời gian ({args.start_date} đến {args.end_date})...")

    ai_predictor = KenoAIPredictor(
        analysis_periods=args.analysis_periods,
        consecutive_enable=args.consecutive_enable,
        consecutive_lookback=args.consecutive_lookback,
        consecutive_threshold=args.consecutive_threshold
    )
    results = ai_predictor.test_date_range(args.start_date, args.end_date)
