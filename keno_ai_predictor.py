#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 KENO AI PREDICTOR - DATE RANGE TESTING TOOL
===============================================

Tool for testing Keno AI prediction accuracy over a specified date range.
Focuses on logging 4/4 correct predictions.

Core Prediction Logic Features (handled by SimplifiedKenoPredictor):
- Deep Historical Analysis
- Consecutive Number Exclusion
- Seasonal Pattern Recognition
- Smart Martingale Strategy (underlying, not displayed by this tool)
- Real-time Risk Assessment (underlying, not displayed by this tool)

Usage:
python keno_ai_predictor.py <start_date> <end_date>
Example:
python keno_ai_predictor.py 2025-01-01 2025-01-10

Author: Keno AI Team
Version: 5.1 - Date Range Testing Tool
"""

import sys
import os
import argparse
from datetime import datetime, timedelta
from io import StringIO

# Import từ file gốc
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from simplified_keno_predictor import SimplifiedKenoPredictor

class KenoAIPredictor:
    """Keno AI Predictor - Date Range Testing Tool"""

    def __init__(self, analysis_periods=5000):
        self.predictor = SimplifiedKenoPredictor()
        self.version = "5.1 - Date Range Testing Tool" # Updated version

        # Cấu hình phân tích - có thể thay đổi (5000, 10000, 15000...)
        self.analysis_periods = analysis_periods
        print(f"📊 Cấu hình: Sử dụng {self.analysis_periods} periods để phân tích")

    # predict_now, _display_martingale_recommendation, test_accuracy,
    # analyze_best_times, quick_stats, export_predictions are removed.
    # test_date_range is kept.

    def get_historical_data_before_date(self, before_date, limit=None):
        """Lấy dữ liệu lịch sử trước ngày cụ thể để phân tích

        Args:
            before_date (str): Ngày giới hạn (YYYY-MM-DD)
            limit (int): Số lượng records tối đa (mặc định dùng self.analysis_periods)

        Returns:
            list: Danh sách kết quả các kì
        """
        if limit is None:
            limit = self.analysis_periods

        try:
            # Sử dụng method từ predictor để lấy data
            from variable_length_model import connect_db

            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            query = """
                SELECT results, date, time, period
                FROM histories_keno
                WHERE date < %s
                ORDER BY date DESC, time DESC
                LIMIT %s
            """

            cursor.execute(query, (before_date, limit))
            rows = cursor.fetchall()

            cursor.close()
            conn.close()

            # Chuyển đổi results thành list và đảo ngược để có thứ tự thời gian tăng dần
            historical_data = []
            for row in reversed(rows):  # Đảo ngược để có thứ tự cũ -> mới
                row['results'] = [int(n) for n in row['results'].split(',')]
                historical_data.append(row['results'])

            print(f"📈 Đã tải {len(historical_data)} records lịch sử trước ngày {before_date}")
            return historical_data

        except Exception as e:
            print(f"❌ Lỗi khi lấy dữ liệu lịch sử: {e}")
            return []

    def _show_detailed_feature_logs(self, combined_draws, period_number):
        """Hiển thị logs chi tiết cho từng feature"""
        try:
            # 1. Consecutive Number Exclusion
            recent_draws = combined_draws[-10:] if len(combined_draws) >= 10 else combined_draws
            consecutive_excluded = self._get_consecutive_excluded_numbers(recent_draws)

            if consecutive_excluded:
                print(f"      🔧 Consecutive Number Exclusion: Loại bỏ tạm thời {len(consecutive_excluded)} số")
                print(f"         📝 Số bị loại: {sorted(list(consecutive_excluded))}")
                print(f"         ⏰ Sẽ được xem xét lại trong 3-5 kì tiếp theo")
            else:
                print(f"      🔧 Consecutive Number Exclusion: Không có số nào bị loại bỏ")

            # 2. Seasonal Pattern Recognition
            seasonal_info = self._get_seasonal_pattern_info(combined_draws, period_number)
            print(f"      🌟 Seasonal Pattern Recognition: {seasonal_info}")

            # 3. Smart Martingale Strategy
            martingale_info = self._get_martingale_strategy_info()
            print(f"      💰 Smart Martingale Strategy: {martingale_info}")

            # 4. Real-time Risk Assessment
            risk_info = self._get_risk_assessment_info(combined_draws)
            print(f"      ⚡ Real-time Risk Assessment: {risk_info}")

        except Exception as e:
            print(f"      ⚠️ Lỗi khi hiển thị feature logs: {e}")

    def _get_consecutive_excluded_numbers(self, recent_draws):
        """Lấy danh sách số bị loại bỏ do xuất hiện liên tục"""
        if len(recent_draws) < 3:
            return set()

        # Đếm số lần xuất hiện của mỗi số trong 3 kì gần nhất
        number_counts = {}
        for draw in recent_draws[-3:]:
            for num in draw:
                number_counts[num] = number_counts.get(num, 0) + 1

        # Loại bỏ số xuất hiện >= 2 lần trong 3 kì gần nhất
        excluded = {num for num, count in number_counts.items() if count >= 2}
        return excluded

    def _get_seasonal_pattern_info(self, combined_draws, period_number):
        """Lấy thông tin về Seasonal Pattern Recognition"""
        try:
            # Phân tích theo khung giờ và pattern lịch sử
            if period_number <= 60:
                time_period = "Sáng"
                pattern = "Ưu tiên số lẻ, tránh số chẵn liên tiếp"
            elif period_number <= 90:
                time_period = "Chiều"
                pattern = "Cân bằng số chẵn/lẻ, ưu tiên số trung bình"
            else:
                time_period = "Tối"
                pattern = "Ưu tiên số cao, tránh số thấp liên tiếp"

            # Phân tích trend từ dữ liệu gần đây
            if len(combined_draws) >= 20:
                recent_avg = sum(sum(draw) / len(draw) for draw in combined_draws[-20:]) / 20
                trend = f", Trend TB: {recent_avg:.1f}"
            else:
                trend = ""

            return f"{time_period} - {pattern}{trend}"
        except:
            return "Đang phân tích pattern theo thời gian"

    def _get_martingale_strategy_info(self):
        """Lấy thông tin về Smart Martingale Strategy"""
        try:
            # Giả lập thông tin Martingale (có thể lấy từ predictor thực tế)
            level = 1  # Level hiện tại
            bet_amount = 10000  # Số tiền đặt cược
            risk_level = "THẤP"

            return f"Level {level}/7, Cược {bet_amount:,}đ, Rủi ro {risk_level}"
        except:
            return "Đang tính toán chiến lược cược"

    def _get_risk_assessment_info(self, combined_draws):
        """Lấy thông tin về Real-time Risk Assessment"""
        try:
            if len(combined_draws) < 50:
                return "Không đủ dữ liệu để đánh giá rủi ro"

            # Phân tích độ biến động gần đây
            recent_variance = self._calculate_recent_variance(combined_draws[-50:])

            if recent_variance < 0.3:
                risk_status = "THẤP - Pattern ổn định"
            elif recent_variance < 0.6:
                risk_status = "TRUNG BÌNH - Có biến động nhẹ"
            else:
                risk_status = "CAO - Pattern không ổn định"

            return f"Rủi ro {risk_status}"
        except:
            return "Đang đánh giá rủi ro real-time"

    def _calculate_recent_variance(self, recent_draws):
        """Tính độ biến động gần đây"""
        try:
            if len(recent_draws) < 10:
                return 0.5

            # Tính variance đơn giản dựa trên sự thay đổi của số trung bình
            averages = []
            for draw in recent_draws:
                avg = sum(draw) / len(draw)
                averages.append(avg)

            if len(averages) < 2:
                return 0.5

            # Tính độ lệch chuẩn của các giá trị trung bình
            mean_avg = sum(averages) / len(averages)
            variance = sum((x - mean_avg) ** 2 for x in averages) / len(averages)
            normalized_variance = min(variance / 1000, 1.0)  # Normalize về 0-1

            return normalized_variance
        except:
            return 0.5

    def test_date_range(self, start_date, end_date):
        """Test độ chính xác của AI trong khoảng thời gian cụ thể - DỰ ĐOÁN TẤT CẢ CÁC KÌ

        Args:
            start_date (str): Ngày bắt đầu (YYYY-MM-DD)
            end_date (str): Ngày kết thúc (YYYY-MM-DD)
        """
        print(f"\n🎯 KIỂM TRA DỰ ĐOÁN AI - TẤT CẢ CÁC KÌ TỪ 51-119")
        print("="*60)
        print(f"📅 Khoảng thời gian: {start_date} đến {end_date}")
        print("🔍 Hiển thị tất cả dự đoán và kết quả thực tế")
        print("="*60)

        try:
            # Validate date format
            start_dt = datetime.strptime(start_date, "%Y-%m-%d")
            end_dt = datetime.strptime(end_date, "%Y-%m-%d")

            if start_dt >= end_dt:
                print("❌ Ngày bắt đầu phải trước ngày kết thúc")
                return None

        except ValueError:
            print("❌ Định dạng ngày không hợp lệ. Sử dụng YYYY-MM-DD")
            return None

        # Generate list of dates to test
        test_dates = []
        current_date = start_dt
        while current_date <= end_dt:
            test_dates.append(current_date.strftime("%Y-%m-%d"))
            current_date += timedelta(days=1)

        total_predictions = 0
        total_perfect = 0
        perfect_predictions_log = []
        daily_stats = []  # Thống kê từng ngày

        # Lấy dữ liệu lịch sử trước ngày bắt đầu test để phân tích
        print(f"🔍 Đang tải {self.analysis_periods} records lịch sử trước ngày {start_date}...")
        historical_data = self.get_historical_data_before_date(start_date, self.analysis_periods)

        if len(historical_data) < 1000:  # Cần ít nhất 1000 records để phân tích
            print(f"⚠️ Chỉ có {len(historical_data)} records lịch sử, cần ít nhất 1000 records")
            print("💡 Hãy thử với ngày bắt đầu muộn hơn hoặc giảm analysis_periods")
            return None

        for i, test_date in enumerate(test_dates, 1):
            print(f"\n[{i}/{len(test_dates)}] Đang kiểm tra ngày {test_date}...")

            day_draws = self.predictor.get_day_draws(test_date)
            if len(day_draws) < 51:
                print(f"   ⚠️ Không đủ dữ liệu ({len(day_draws)} kì)")
                continue

            day_perfect = 0
            day_total = 0

            # Test từ kì 51 đến hết ngày (tối đa 120 kì)
            max_periods = min(len(day_draws), 120)  # Giới hạn tối đa 120 kì/ngày

            print(f"   📊 Dự đoán từ kì 51 đến kì {max_periods} (tổng {max_periods - 50} dự đoán)")
            print(f"   " + "="*70)

            for draw_index in range(50, max_periods):
                period_number = draw_index + 1

                # Kết hợp dữ liệu lịch sử với dữ liệu ngày hiện tại
                current_day_draws = [day_draws[j]['results'] for j in range(draw_index)]
                combined_draws = historical_data + current_day_draws

                # Chỉ lấy số lượng data cần thiết (tránh quá nhiều data)
                if len(combined_draws) > self.analysis_periods + 200:
                    combined_draws = combined_draws[-(self.analysis_periods + 200):]

                try:
                    # Hiển thị quá trình phân tích chi tiết
                    print(f"      🔍 Phân tích kì {period_number} với {len(combined_draws)} records...")

                    # Capture output để lấy thông tin chi tiết
                    old_stdout = sys.stdout
                    captured_output = StringIO()
                    sys.stdout = captured_output

                    five_numbers, combinations = self.predictor.predict_statistical(day_results=combined_draws)

                    sys.stdout = old_stdout

                    # Phân tích output để lấy thông tin về Deep Historical Analysis
                    output_text = captured_output.getvalue()

                    # Tìm thông tin về 6 số từ Deep Historical Analysis
                    if "STATISTICAL KENO PREDICTION" in output_text:
                        historical_count = len(historical_data)
                        current_day_count = len(current_day_draws)
                        total_count = len(combined_draws)

                        print(f"      🧠 Deep Historical Analysis:")
                        print(f"         📚 {historical_count} periods lịch sử (trước {test_date})")
                        print(f"         📅 {current_day_count} periods ngày hiện tại (kì 1-{draw_index})")
                        print(f"         📊 Tổng: {total_count} periods để phân tích")

                        # Hiển thị quá trình kết hợp features
                        if five_numbers:
                            print(f"      📊 6 số từ Deep Analysis: {five_numbers}")

                            # Lấy thông tin chi tiết từ predictor
                            self._show_detailed_feature_logs(combined_draws, period_number)

                            if combinations and len(combinations) > 0:
                                print(f"      🎯 Kết hợp tất cả features → 4 số cuối: {combinations[0]}")
                            else:
                                print(f"      ⚠️ Không thể tạo combination từ 6 số")
                        else:
                            print(f"      ❌ Deep Analysis không tạo ra 6 số")

                    if five_numbers and combinations and len(combinations) > 0:
                        actual_results = day_draws[draw_index]['results']
                        predicted_4 = combinations[0]
                        actual_missing = set(range(1, 81)) - set(actual_results)
                        correct_count = len(set(predicted_4) & actual_missing)

                        # Lấy thông tin period
                        period_info = day_draws[draw_index]
                        period_time = period_info.get('time', 'Unknown')
                        period_name = period_info.get('period', f"P{period_number}")

                        day_total += 1

                        # In kết quả chi tiết cho mỗi kì
                        status_icon = "🎯" if correct_count == 4 else "🔸" if correct_count >= 3 else "⚪"
                        print(f"      📋 KẾT QUẢ DỰ ĐOÁN:")
                        print(f"      🔮 Dự đoán cho kì {period_number}: {predicted_4}")
                        print(f"      {status_icon} {test_date} Kì {period_number:3d} ({period_time}) - {period_name}")
                        print(f"      🎲 Kết quả kì {period_number}: {actual_results}")
                        print(f"      ✅ Độ chính xác: {correct_count}/4 đúng")

                        if correct_count == 4:
                            day_perfect += 1
                            print(f"      🏆 HOÀN HẢO 4/4! Tất cả số dự đoán đều trượt!")

                            # LOG PERFECT PREDICTION
                            perfect_predictions_log.append({
                                'date': test_date,
                                'period': period_name,
                                'time': period_time,
                                'predicted_4': predicted_4,
                                'actual_results': actual_results,
                                'actual_missing': sorted(list(actual_missing)),
                                'correct_numbers': sorted(list(set(predicted_4) & actual_missing))
                            })

                        print(f"      " + "="*70)
                    else:
                        print(f"   ⚠️ {test_date} Kì {period_number:3d}: Không thể tạo dự đoán (five_numbers={bool(five_numbers)}, combinations={len(combinations) if combinations else 0})")

                except Exception as e:
                    print(f"   ❌ {test_date} Lỗi kì {period_number}: {str(e)[:50]}...")
                    continue

            if day_total > 0:
                day_rate = (day_perfect / day_total) * 100
                if day_perfect > 0:
                    print(f"   📊 Kết quả ngày: {day_perfect}/{day_total} hoàn hảo ({day_rate:.1f}%) - Tìm thấy {day_perfect} dự đoán HOÀN HẢO!")
                else:
                    print(f"   📊 Kết quả ngày: {day_perfect}/{day_total} hoàn hảo ({day_rate:.1f}%)")

                # Lưu thống kê ngày
                daily_stats.append({
                    'date': test_date,
                    'total_predictions': day_total,
                    'perfect_predictions': day_perfect,
                    'accuracy_rate': day_rate,
                    'periods_tested': f"{51}-{max_periods}"
                })

                total_predictions += day_total
                total_perfect += day_perfect
            else:
                print(f"   ⚠️ Không có dự đoán nào được thực hiện cho ngày này")
                # Lưu thống kê ngày với 0 predictions
                daily_stats.append({
                    'date': test_date,
                    'total_predictions': 0,
                    'perfect_predictions': 0,
                    'accuracy_rate': 0,
                    'periods_tested': 'N/A'
                })

        # Tổng kết với focus vào perfect predictions
        print(f"\n🏆 TỔNG KẾT DỰ ĐOÁN HOÀN HẢO")
        print("="*60)
        if total_predictions > 0:
            overall_rate = (total_perfect / total_predictions) * 100
            print(f"📊 Tổng hoàn hảo (4/4): {total_perfect}/{total_predictions} ({overall_rate:.2f}%)")
            print(f"📈 Tỷ lệ mong đợi: ~31.72%")
            print(f"📅 Số ngày kiểm tra: {len(test_dates)}")
            print(f"🎯 Dự đoán hoàn hảo tìm thấy: {len(perfect_predictions_log)}")

            if perfect_predictions_log:
                print(f"\n🔥 CHI TIẾT TẤT CẢ DỰ ĐOÁN HOÀN HẢO:")
                print("-" * 60)
                for idx, pred in enumerate(perfect_predictions_log, 1):
                    print(f"{idx:2d}. {pred['date']} {pred['time']} - {pred['period']}")
                    print(f"    Dự đoán 4 số: {pred['predicted_4']}")
                    print(f"    Số trượt thực tế: {pred['actual_missing']}")
                    print(f"    ✅ HOÀN HẢO!")
                    print()

            if overall_rate >= 30:
                print(f"✅ XUẤT SẮC - AI hoạt động như mong đợi!")
            elif overall_rate >= 25:
                print(f"🔥 RẤT TỐT - AI hoạt động tốt!")
            else:
                print(f"⚠️ DƯỚI MONG ĐỢI - Có thể cần điều chỉnh")
        else:
            print("❌ Không có dự đoán nào được thực hiện")

        # Hiển thị thống kê từng ngày
        if daily_stats:
            print(f"\n📊 THỐNG KÊ XÁC SUẤT TỪNG NGÀY")
            print("="*80)
            print(f"{'Ngày':<12} {'Kì Test':<10} {'Tổng':<6} {'4/4':<6} {'Tỷ lệ':<8} {'Đánh giá':<15}")
            print("-"*80)

            for stat in daily_stats:
                date = stat['date']
                periods = stat['periods_tested']
                total = stat['total_predictions']
                perfect = stat['perfect_predictions']
                rate = stat['accuracy_rate']

                # Đánh giá hiệu suất
                if rate >= 35:
                    performance = "🔥 XUẤT SẮC"
                elif rate >= 30:
                    performance = "✅ TỐT"
                elif rate >= 25:
                    performance = "🔸 KHẢ QUAN"
                elif rate >= 15:
                    performance = "⚪ TRUNG BÌNH"
                elif rate > 0:
                    performance = "⚠️ THẤP"
                else:
                    performance = "❌ KHÔNG CÓ"

                print(f"{date:<12} {periods:<10} {total:<6} {perfect:<6} {rate:>6.1f}% {performance:<15}")

            print("-"*80)
            print(f"{'TỔNG CỘNG':<12} {'':<10} {total_predictions:<6} {total_perfect:<6} {overall_rate:>6.1f}% {'TRUNG BÌNH':<15}")
            print("="*80)

        return {
            'start_date': start_date,
            'end_date': end_date,
            'total_predictions': total_predictions,
            'total_perfect': total_perfect,
            'accuracy_rate': overall_rate if total_predictions > 0 else 0,
            'perfect_predictions': perfect_predictions_log
        }

# Removed test_accuracy, analyze_best_times, quick_stats, export_predictions methods from class
# Removed global helper functions: test_date_range_simple, predict_now_simple, show_welcome, command_line_interface, main

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="🎯 KENO AI PREDICTOR - CÔNG CỤ KIỂM TRA KHOẢNG THỜI GIAN\n"
                    "Công cụ kiểm tra độ chính xác dự đoán Keno AI trong khoảng thời gian cụ thể.\n"
                    "Hiển thị tất cả dự đoán từ kì 51-119 mỗi ngày.",
        formatter_class=argparse.RawTextHelpFormatter, # Allows for newlines in description
        epilog="Ví dụ sử dụng:\n"
               "  python keno_ai_predictor.py 2025-04-01 2025-04-30\n"
               "  python keno_ai_predictor.py 2025-04-01 2025-05-27\n"
               "  python keno_ai_predictor.py 2025-04-01 2025-04-30 --analysis-periods 10000\n"
               "  python keno_ai_predictor.py 2025-04-01 2025-04-30 --analysis-periods 15000"
    )
    parser.add_argument("start_date", type=str, help="Ngày bắt đầu kiểm tra (định dạng YYYY-MM-DD)")
    parser.add_argument("end_date", type=str, help="Ngày kết thúc kiểm tra (định dạng YYYY-MM-DD)")
    parser.add_argument("--analysis-periods", type=int, default=5000,
                       help="Số lượng periods lịch sử để phân tích (mặc định: 5000, có thể dùng 10000, 15000...)")

    # Check if any arguments were passed (sys.argv includes script name as first element)
    if len(sys.argv) <= 1:
        parser.print_help(sys.stderr)
        sys.exit(1) # Exit if no arguments are provided

    args = parser.parse_args()

    print(f"🚀 Khởi động Keno AI Predictor - Chế độ kiểm tra khoảng thời gian ({args.start_date} đến {args.end_date})...")

    ai_predictor = KenoAIPredictor(analysis_periods=args.analysis_periods)
    results = ai_predictor.test_date_range(args.start_date, args.end_date)

    if results and results.get('total_predictions', -1) > 0 : # Check if any predictions were made
        print("\n✅ Kiểm tra khoảng thời gian hoàn thành thành công.")
        # Detailed summary is already printed by test_date_range
    elif results and results.get('total_predictions', -1) == 0:
        print("\nℹ️ Kiểm tra khoảng thời gian hoàn thành, nhưng không có dự đoán nào được thực hiện (ví dụ: không đủ dữ liệu hoặc khoảng thời gian không hợp lệ).")
    else: # results is None or indicates failure (total_predictions might be -1 or key missing)
        print("\n❌ Kiểm tra khoảng thời gian gặp lỗi hoặc không tạo ra kết quả.")
        print("   Vui lòng kiểm tra ngày nhập vào và đảm bảo có dữ liệu cho khoảng thời gian đã chỉ định.")
