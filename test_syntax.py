#!/usr/bin/env python3
"""
Test syntax của keno_ai_predictor.py
"""

import ast
import sys

def check_syntax(filename):
    """Kiểm tra syntax của file Python"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            source = f.read()
        
        # Parse AST để kiểm tra syntax
        ast.parse(source)
        print(f"✅ {filename}: Syntax OK")
        return True
        
    except SyntaxError as e:
        print(f"❌ {filename}: Syntax Error at line {e.lineno}")
        print(f"   {e.msg}")
        return False
    except Exception as e:
        print(f"❌ {filename}: Error - {e}")
        return False

if __name__ == "__main__":
    filename = "keno_ai_predictor.py"
    if check_syntax(filename):
        print("🎉 File syntax is valid!")
    else:
        print("💥 File has syntax errors!")
